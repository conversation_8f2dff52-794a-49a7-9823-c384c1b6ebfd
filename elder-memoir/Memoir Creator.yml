app:
  description: ''
  icon: woman-wearing-turban
  icon_background: '#FFEAD5'
  mode: workflow
  name: MemoirCreator
  use_icon_as_answer_icon: false
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - remote_url
        - local_file
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      configs: []
      enabled: false
      type: ''
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: start
        targetType: llm
      id: start-llm
      source: start
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: llm-source-1738922239357-target
      source: llm
      sourceHandle: source
      target: '1738922239357'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: 1738922239357-source-1738922452173-target
      source: '1738922239357'
      sourceHandle: source
      target: '1738922452173'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: iteration
      id: 1739153642040-source-1739153289026-target
      source: '1739153642040'
      sourceHandle: source
      target: '1739153289026'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: code
      id: 1738922452173-source-1739153642040-target
      source: '1738922452173'
      sourceHandle: source
      target: '1739153642040'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        iteration_id: '1739153289026'
        sourceType: iteration-start
        targetType: llm
      id: 1739153289026start-source-1739260331874-target
      source: 1739153289026start
      sourceHandle: source
      target: '1739260331874'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        iteration_id: '1739153289026'
        sourceType: llm
        targetType: variable-aggregator
      id: 1739260331874-source-1739261276554-target
      source: '1739260331874'
      sourceHandle: source
      target: '1739261276554'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        sourceType: iteration
        targetType: template-transform
      id: 1739153289026-source-1739337973364-target
      source: '1739153289026'
      sourceHandle: source
      target: '1739337973364'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: end
      id: 1739337973364-source-1739261312720-target
      source: '1739337973364'
      sourceHandle: source
      target: '1739261312720'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        selected: false
        title: Input
        type: start
        variables:
        - allowed_file_extensions: []
          allowed_file_types: []
          allowed_file_upload_methods: []
          description: ''
          label: Name
          max_length: null
          options: []
          required: true
          type: paragraph
          variable: Name
        - label: Background
          max_length: 500
          options: []
          required: true
          type: paragraph
          variable: Background
      height: 116
      id: start
      position:
        x: 30
        y: 284
      positionAbsolute:
        x: 30
        y: 284
      selected: false
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        memory: null
        model:
          completion_params:
            frequency_penalty: 0.1
            presence_penalty: 0.1
            stop: []
            temperature: 0.8
            top_p: 0.9
          mode: chat
          name: gpt-35-turbo
          provider: azure_openai
        prompt_template:
        - edition_type: basic
          id: 8a3ae375-2bce-41b3-bba8-5786541e7d11
          role: system
          text: 'You are Ms.Liu, a professional journalist and author.


            You know the story steps:


            These eight steps are:

            1. You A character in their zone of comfort

            2. Need wants something

            3. Go! so they enter an unfamiliar situation

            4. Struggle to which they have to adapt

            5. Find in order to get what they want

            6. Suffer yet they have to make a sacrifice

            7. Return before they return to their familiar situation

            8. Change having changed fundamentally



            You follow five essential tips for writing the Memoir.


            1. Think Like a Novelist

            - Treat your memoir as a storytelling endeavor

            - Focus on narrative tension and structure

            - Don''t feel compelled to follow strict chronological order

            - Unlike autobiography, you don''t need to include every detail

            - Create suspense and maintain reader engagement

            - Build a coherent framework that allows for creative storytelling


            2. Establish Your Narrative Voice Early

            - Set your personal tone from the beginning

            - Incorporate your unique dialect and speech patterns

            - Read aloud and record to verify authenticity

            - Build reader trust through consistent voice

            - Make your reader understand your perspective early

            Consider Maya Angelou''s "I Know Why the Caged Bird Sings" as an example


            3. Treat People as Characters

            - Step back and view people objectively

            - Develop full character profiles including:

            - Hopes and fears

            - Dreams and desires

            - Unique habits and traits

            - Don''t assume readers know your personal connections

            - Consider legal and privacy implications when writing about living people

            - Show rather than tell character traits


            4. Allow Readers Their Own Judgment

            - Avoid over-directing reader interpretations

            - Present events without excessive personal bias

            - Create space for reader interpretation

            - Don''t manipulate opinions on politics or moral issues

            - Focus on telling the story rather than forcing conclusions

            - Let readers draw their own meaningful connections


            5. Maintain Your Central Theme

            - Focus on a specific theme or life moment

            - Use theme to guide content selection

            - Be selective about what to include or cut

            - Consider your purpose and audience

            - Keep writing focused and purposeful



            '
        - id: 3aa7969c-b647-497d-841c-f3d8ee6dc1cd
          role: user
          text: "You are doing memoir for an elder named: {{#start.Name#}}, \n\nTo\
            \ gather information for memoir creation, please help analyze based on\
            \ the {{#start.Background#}}, and propose enough questions related to\
            \ memoir creation. \n\nPlease remember, a memoir has to satisfy the client,\
            \ the elder, and the family. "
        selected: false
        title: MsLiu-Questions
        type: llm
        vision:
          configs: null
          enabled: false
          variable_selector: null
      height: 98
      id: llm
      position:
        x: 329.40749191020836
        y: 284
      positionAbsolute:
        x: 329.40749191020836
        y: 284
      selected: false
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - llm
          - text
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: gpt-35-turbo
          provider: azure_openai
        prompt_template:
        - edition_type: basic
          id: 6c5d2116-911d-4851-b538-e27292b03b40
          role: system
          text: "You are an elder with below background,\nName: {{#start.Name#}},\
            \ \n{{#start.Background#}}.\n\n\n"
        - id: e5238dfb-28a1-4e1e-8efa-a88467cfc946
          role: user
          text: "Ms.Liu is your memoir author, helping you writing memoir. \nShe has\
            \ given you some questions as {{#llm.text#}}\n\nPlease answer all of to\
            \ Mr.Liu. \nKeep your role consistent. \nFor the memoir, please remember\
            \ those are part of your legacy to your future generations, especially\
            \ your grandchildren, who may use it to read your life, get inspirations,\
            \ know you better. \n\nMake your answers very good for Memoir writing.\
            \ \nIf you have extra things to highlight, feel free to add them. "
        selected: false
        title: Elder-ANSWERS
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1738922239357'
      position:
        x: 638
        y: 284
      positionAbsolute:
        x: 638
        y: 284
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            frequency_penalty: 0.3
            presence_penalty: 0.2
            response_format: json_object
            temperature: 0.1
            top_p: 0.85
          mode: chat
          name: gpt-35-turbo
          provider: azure_openai
        prompt_template:
        - id: 12510977-ebbc-4921-a85a-e88dbd29c8aa
          role: system
          text: 'You are Ms.Liu, a professional journalist and author.


            You follow Five Essential Tips for Writing and Editing Your Memoir


            1. Think Like a Novelist

            - Treat your memoir as a storytelling endeavor

            - Focus on narrative tension and structure

            - Don''t feel compelled to follow strict chronological order

            - Unlike autobiography, you don''t need to include every detail

            - Create suspense and maintain reader engagement

            - Build a coherent framework that allows for creative storytelling


            2. Establish Your Narrative Voice Early

            - Set your personal tone from the beginning

            - Incorporate your unique dialect and speech patterns

            - Read aloud and record to verify authenticity

            - Build reader trust through consistent voice

            - Make your reader understand your perspective early

            Consider Maya Angelou''s "I Know Why the Caged Bird Sings" as an example


            3. Treat People as Characters

            - Step back and view people objectively

            - Develop full character profiles including:

            - Hopes and fears

            - Dreams and desires

            - Unique habits and traits

            - Don''t assume readers know your personal connections

            - Consider legal and privacy implications when writing about living people

            - Show rather than tell character traits


            4. Allow Readers Their Own Judgment

            - Avoid over-directing reader interpretations

            - Present events without excessive personal bias

            - Create space for reader interpretation

            - Don''t manipulate opinions on politics or moral issues

            - Focus on telling the story rather than forcing conclusions

            - Let readers draw their own meaningful connections


            5. Maintain Your Central Theme

            - Focus on a specific theme or life moment

            - Use theme to guide content selection

            - Be selective about what to include or cut

            - Consider your purpose and audience

            - Keep writing focused and purposeful



            '
        - id: f8018d10-3764-4f94-a836-1c0a67a5c569
          role: user
          text: "You are doing memoir for an elder.\nBelow is the Elder information:\
            \ \nName: {{#start.Name#}}, \nBackground: {{#start.Background#}},\n\n\
            You interviewed {{#start.Name#}} with these questions {{#llm.text#}},\
            \ and the elder has answered as\n{{#1738922239357.text#}}\n\nPlease plan\
            \ the Memoir outline for {{#start.Name#}}, including title, summary, length,\
            \ in Chinese. \n- title: the title of the chapter\n- summary: rough idea\
            \ of this chapter\n- length: words you expect to spend on this chapter\n\
            - memo: notes you kept for this chapter to remind yourself when you actually\
            \ work on this chapter. \n\n\nOutput in pure json array, no formatting,\
            \ no anything, it must be a parseable json! \n```\n[\n{\"title\":\"第一章\"\
            , \"summary\":\"...\", \"length\":\"2000字\", \"memo\":\"\"},\n{\"title\"\
            :\"第二章\", \"summary\":\"...\", \"length\":\"2000字\", \"memo\":\"\"}, \n\
            ...\n]\n```"
        retry_config:
          max_retries: 3
          retry_enabled: false
          retry_interval: 1000
        selected: false
        title: MSLIU-OUTLINING
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1738922452173'
      position:
        x: 942
        y: 284
      positionAbsolute:
        x: 942
        y: 284
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        error_handle_mode: continue-on-error
        height: 315
        is_parallel: true
        iterator_selector:
        - '1739153642040'
        - result
        output_selector:
        - '1739261276554'
        - Group1
        output_type: array[object]
        parallel_nums: 10
        selected: false
        start_node_id: 1739153289026start
        title: 迭代
        type: iteration
        width: 905.5780915635187
      height: 315
      id: '1739153289026'
      position:
        x: 1839.2976084022303
        y: 564.1054144962569
      positionAbsolute:
        x: 1839.2976084022303
        y: 564.1054144962569
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 906
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1739153289026start
      parentId: '1739153289026'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 1863.2976084022303
        y: 632.1054144962569
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        code: "def main(json_input: str) -> dict:\n    import json\n    # 1. 首先确保JSON字符串格式正确\n\
          \    json_input = json_input.replace('\\n', '').replace('\\\\n', '\\n').replace('\\\
          \\\"', '\"')\n    \n    # 2. 解析JSON\n    data = json.loads(json_input)\n\
          \    \n    # 3. 使用字典访问方式\n    chapters = []\n    for item in data['memoirOutline']:\
          \  # 使用data['memoirOutline']而不是data.memoirOutline\n        chapter = f\"\
          Title: {item['title']}, Summary: {item['summary']}, Memo: {item['memo']},\
          \ Length: {item['length']}\"\n        chapters.append(chapter)\n    \n \
          \   return {'result': chapters}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[string]
        selected: false
        title: PER-CHAPTER
        type: code
        variables:
        - value_selector:
          - '1738922452173'
          - text
          variable: json_input
      height: 54
      id: '1739153642040'
      position:
        x: 1331.5699679231102
        y: 482.5409815212746
      positionAbsolute:
        x: 1331.5699679231102
        y: 482.5409815212746
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1739153642040'
          - result
        desc: ''
        isInIteration: true
        iteration_id: '1739153289026'
        model:
          completion_params:
            frequency_penalty: 0.1
            presence_penalty: 0.1
            temperature: 0.8
            top_p: 0.9
          mode: chat
          name: gpt-35-turbo
          provider: azure_openai
        prompt_template:
        - id: ea590062-2efd-43e0-ac13-e119e344ba3d
          role: system
          text: 'You are Ms.Liu, a professional journalist and author.



            You know the story steps:


            These eight steps are:

            1. You A character in their zone of comfort

            2. Need wants something

            3. Go! so they enter an unfamiliar situation

            4. Struggle to which they have to adapt

            5. Find in order to get what they want

            6. Suffer yet they have to make a sacrifice

            7. Return before they return to their familiar situation

            8. Change having changed fundamentally


            You follow Five Essential Tips for Writing and Editing Your Memoir


            1. Think Like a Novelist

            - Treat your memoir as a storytelling endeavor

            - Focus on narrative tension and structure

            - Don''t feel compelled to follow strict chronological order

            - Unlike autobiography, you don''t need to include every detail

            - Create suspense and maintain reader engagement

            - Build a coherent framework that allows for creative storytelling


            2. Establish Your Narrative Voice Early

            - Set your personal tone from the beginning

            - Incorporate your unique dialect and speech patterns

            - Read aloud and record to verify authenticity

            - Build reader trust through consistent voice

            - Make your reader understand your perspective early

            Consider Maya Angelou''s "I Know Why the Caged Bird Sings" as an example


            3. Treat People as Characters

            - Step back and view people objectively

            - Develop full character profiles including:

            - Hopes and fears

            - Dreams and desires

            - Unique habits and traits

            - Don''t assume readers know your personal connections

            - Consider legal and privacy implications when writing about living people

            - Show rather than tell character traits


            4. Allow Readers Their Own Judgment

            - Avoid over-directing reader interpretations

            - Present events without excessive personal bias

            - Create space for reader interpretation

            - Don''t manipulate opinions on politics or moral issues

            - Focus on telling the story rather than forcing conclusions

            - Let readers draw their own meaningful connections


            5. Maintain Your Central Theme

            - Focus on a specific theme or life moment

            - Use theme to guide content selection

            - Be selective about what to include or cut

            - Consider your purpose and audience

            - Keep writing focused and purposeful



            '
        - id: 865fa23f-ae82-4175-be8a-7eda81cfe9bf
          role: user
          text: "You are doing memoir for an elder:\nName: {{#start.Name#}}, \n\n\
            You have asked questions {{#llm.text#}}  for memoir creation,  and got\
            \ answers as {{#1738922239357.text#}}. \n\nAccording to above information\
            \ you gathered, you have planned a complete table of content{{#1739153642040.result#}}.\n\
            \nNow you are working on this chapter {{#1739153289026.item#}}, please\
            \ make sure the current chapter can work with the previous chapter and\
            \ next chapter topics. \n\nPlease focus on the given chapter, stick to\
            \ you plan. \nRemember chapters have to interconnect with each other.\
            \ No conflicts. \n"
        selected: false
        title: Chapters
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1739260331874'
      parentId: '1739153289026'
      position:
        x: 273.65275940518904
        y: 111.15601680253906
      positionAbsolute:
        x: 2112.9503678074193
        y: 675.261431298796
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        advanced_settings:
          group_enabled: true
          groups:
          - groupId: 59364c14-adb5-4459-913b-4f71c378052f
            group_name: Group1
            output_type: string
            variables:
            - - '1739260331874'
              - text
        desc: ''
        isInIteration: true
        iteration_id: '1739153289026'
        output_type: string
        selected: false
        title: 变量聚合器
        type: variable-aggregator
        variables:
        - - '1739260331874'
          - text
      height: 109
      id: '1739261276554'
      parentId: '1739153289026'
      position:
        x: 576.4076092226924
        y: 109.91086662004238
      positionAbsolute:
        x: 2415.7052176249226
        y: 674.0162811162993
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1739337973364'
          - output
          variable: output
        selected: false
        title: 结束 2
        type: end
      height: 90
      id: '1739261312720'
      position:
        x: 3374.5094141825007
        y: 574.776793273298
      positionAbsolute:
        x: 3374.5094141825007
        y: 574.776793273298
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "    {%- for item in chapters %}\n        {{ item.output }}\n  \
          \  {%- endfor %}"
        title: 模板转换
        type: template-transform
        variables:
        - value_selector:
          - '1739153289026'
          - output
          variable: chapters
      height: 54
      id: '1739337973364'
      position:
        x: 3064.5016404232547
        y: 574.776793273298
      positionAbsolute:
        x: 3064.5016404232547
        y: 574.776793273298
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -1354.1025593005631
      y: -143.2526421998084
      zoom: 0.8031159727213576
