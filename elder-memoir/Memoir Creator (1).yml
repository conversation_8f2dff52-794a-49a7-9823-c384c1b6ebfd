app:
  description: ''
  icon: woman-wearing-turban
  icon_background: '#FFEAD5'
  mode: workflow
  name: MemoirCreator-New
  use_icon_as_answer_icon: false
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - remote_url
        - local_file
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      configs: []
      enabled: false
      type: ''
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: start
        targetType: llm
      id: start-llm
      selected: false
      source: start
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: llm-source-1738922239357-target
      selected: false
      source: llm
      sourceHandle: source
      target: '1738922239357'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: 1738922239357-source-1738922452173-target
      selected: false
      source: '1738922239357'
      sourceHandle: source
      target: '1738922452173'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: code
        targetType: iteration
      id: 1739153642040-source-1739153289026-target
      selected: false
      source: '1739153642040'
      sourceHandle: source
      target: '1739153289026'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: code
      id: 1738922452173-source-1739153642040-target
      selected: false
      source: '1738922452173'
      sourceHandle: source
      target: '1739153642040'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        iteration_id: '1739153289026'
        sourceType: iteration-start
        targetType: llm
      id: 1739153289026start-source-1739260331874-target
      selected: false
      source: 1739153289026start
      sourceHandle: source
      target: '1739260331874'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        sourceType: iteration
        targetType: template-transform
      id: 1739153289026-source-1739337973364-target
      selected: false
      source: '1739153289026'
      sourceHandle: source
      target: '1739337973364'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: end
      id: 1739337973364-source-1739261312720-target
      selected: false
      source: '1739337973364'
      sourceHandle: source
      target: '1739261312720'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        iteration_id: '1739153289026'
        sourceType: llm
        targetType: llm
      id: 1739260331874-source-1739344881577-target
      selected: false
      source: '1739260331874'
      sourceHandle: source
      target: '1739344881577'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        iteration_id: '1739153289026'
        sourceType: llm
        targetType: llm
      id: 1739344881577-source-1739345108342-target
      selected: false
      source: '1739344881577'
      sourceHandle: source
      target: '1739345108342'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        iteration_id: '1739153289026'
        sourceType: llm
        targetType: llm
      id: 1739345108342-source-1739347859290-target
      selected: false
      source: '1739345108342'
      sourceHandle: source
      target: '1739347859290'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        iteration_id: '1739153289026'
        sourceType: llm
        targetType: template-transform
      id: 1739347859290-source-1739354140618-target
      source: '1739347859290'
      sourceHandle: source
      target: '1739354140618'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: true
        iteration_id: '1739153289026'
        sourceType: template-transform
        targetType: variable-aggregator
      id: 1739354140618-source-1739261276554-target
      source: '1739354140618'
      sourceHandle: source
      target: '1739261276554'
      targetHandle: target
      type: custom
      zIndex: 1002
    nodes:
    - data:
        selected: false
        title: Input
        type: start
        variables:
        - allowed_file_extensions: []
          allowed_file_types: []
          allowed_file_upload_methods: []
          description: ''
          label: Name
          max_length: null
          options: []
          required: true
          type: paragraph
          variable: Name
        - label: Background
          max_length: 500
          options: []
          required: true
          type: paragraph
          variable: Background
      height: 116
      id: start
      position:
        x: 421.68876494651323
        y: 455.95635260206353
      positionAbsolute:
        x: 421.68876494651323
        y: 455.95635260206353
      selected: false
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        memory: null
        model:
          completion_params:
            frequency_penalty: 0.1
            presence_penalty: 0.1
            stop: []
            temperature: 0.8
            top_p: 0.9
          mode: chat
          name: google/gemini-2.0-flash-001
          provider: openrouter
        prompt_template:
        - edition_type: basic
          id: 8a3ae375-2bce-41b3-bba8-5786541e7d11
          role: system
          text: 'You are Ms.Liu, a professional journalist and author.


            You know the story steps:


            These eight steps are:

            1. You A character in their zone of comfort

            2. Need wants something

            3. Go! so they enter an unfamiliar situation

            4. Struggle to which they have to adapt

            5. Find in order to get what they want

            6. Suffer yet they have to make a sacrifice

            7. Return before they return to their familiar situation

            8. Change having changed fundamentally



            You follow five essential tips for writing the Memoir.


            1. Think Like a Novelist

            - Treat your memoir as a storytelling endeavor

            - Focus on narrative tension and structure

            - Don''t feel compelled to follow strict chronological order

            - Unlike autobiography, you don''t need to include every detail

            - Create suspense and maintain reader engagement

            - Build a coherent framework that allows for creative storytelling


            2. Establish Your Narrative Voice Early

            - Set your personal tone from the beginning

            - Incorporate your unique dialect and speech patterns

            - Read aloud and record to verify authenticity

            - Build reader trust through consistent voice

            - Make your reader understand your perspective early

            Consider Maya Angelou''s "I Know Why the Caged Bird Sings" as an example


            3. Treat People as Characters

            - Step back and view people objectively

            - Develop full character profiles including:

            - Hopes and fears

            - Dreams and desires

            - Unique habits and traits

            - Don''t assume readers know your personal connections

            - Consider legal and privacy implications when writing about living people

            - Show rather than tell character traits


            4. Allow Readers Their Own Judgment

            - Avoid over-directing reader interpretations

            - Present events without excessive personal bias

            - Create space for reader interpretation

            - Don''t manipulate opinions on politics or moral issues

            - Focus on telling the story rather than forcing conclusions

            - Let readers draw their own meaningful connections


            5. Maintain Your Central Theme

            - Focus on a specific theme or life moment

            - Use theme to guide content selection

            - Be selective about what to include or cut

            - Consider your purpose and audience

            - Keep writing focused and purposeful



            '
        - id: 3aa7969c-b647-497d-841c-f3d8ee6dc1cd
          role: user
          text: "You are doing memoir for an elder named: {{#start.Name#}}, \n\nTo\
            \ gather information for memoir creation, please help analyze based on\
            \ the {{#start.Background#}}, and propose enough questions related to\
            \ memoir creation. \n\nPlease remember, a memoir has to satisfy the client,\
            \ the elder, and the family. "
        selected: false
        title: MsLiu-Questions
        type: llm
        vision:
          configs: null
          enabled: false
          variable_selector: null
      height: 98
      id: llm
      position:
        x: 706.9632609772481
        y: 455.95635260206353
      positionAbsolute:
        x: 706.9632609772481
        y: 455.95635260206353
      selected: false
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - llm
          - text
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: google/gemini-2.0-flash-001
          provider: openrouter
        prompt_template:
        - edition_type: basic
          id: 6c5d2116-911d-4851-b538-e27292b03b40
          role: system
          text: "You are an elder with below background,\nName: {{#start.Name#}},\
            \ \n{{#start.Background#}}.\n\n\n"
        - id: e5238dfb-28a1-4e1e-8efa-a88467cfc946
          role: user
          text: "Ms.Liu is your memoir author, helping you writing memoir. \nShe has\
            \ given you some questions as {{#llm.text#}}\n\nPlease answer all of to\
            \ Mr.Liu. \nKeep your role consistent. \nFor the memoir, please remember\
            \ those are part of your legacy to your future generations, especially\
            \ your grandchildren, who may use it to read your life, get inspirations,\
            \ know you better. \n\nMake your answers very good for Memoir writing.\
            \ \nIf you have extra things to highlight, feel free to add them. "
        selected: false
        title: Elder-ANSWERS
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1738922239357'
      position:
        x: 701.8513394016577
        y: 689.5553779820928
      positionAbsolute:
        x: 701.8513394016577
        y: 689.5553779820928
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            frequency_penalty: 0.3
            presence_penalty: 0.2
            response_format: json_object
            temperature: 0.6
            top_p: 0.85
          mode: chat
          name: google/gemini-2.0-flash-001
          provider: openrouter
        prompt_template:
        - id: 12510977-ebbc-4921-a85a-e88dbd29c8aa
          role: system
          text: 'You are Ms.Liu, a professional journalist and author.


            You follow Five Essential Tips for Writing and Editing Your Memoir


            1. Think Like a Novelist

            - Treat your memoir as a storytelling endeavor

            - Focus on narrative tension and structure

            - Don''t feel compelled to follow strict chronological order

            - Unlike autobiography, you don''t need to include every detail

            - Create suspense and maintain reader engagement

            - Build a coherent framework that allows for creative storytelling


            2. Establish Your Narrative Voice Early

            - Set your personal tone from the beginning

            - Incorporate your unique dialect and speech patterns

            - Read aloud and record to verify authenticity

            - Build reader trust through consistent voice

            - Make your reader understand your perspective early

            Consider Maya Angelou''s "I Know Why the Caged Bird Sings" as an example


            3. Treat People as Characters

            - Step back and view people objectively

            - Develop full character profiles including:

            - Hopes and fears

            - Dreams and desires

            - Unique habits and traits

            - Don''t assume readers know your personal connections

            - Consider legal and privacy implications when writing about living people

            - Show rather than tell character traits


            4. Allow Readers Their Own Judgment

            - Avoid over-directing reader interpretations

            - Present events without excessive personal bias

            - Create space for reader interpretation

            - Don''t manipulate opinions on politics or moral issues

            - Focus on telling the story rather than forcing conclusions

            - Let readers draw their own meaningful connections


            5. Maintain Your Central Theme

            - Focus on a specific theme or life moment

            - Use theme to guide content selection

            - Be selective about what to include or cut

            - Consider your purpose and audience

            - Keep writing focused and purposeful



            '
        - id: f8018d10-3764-4f94-a836-1c0a67a5c569
          role: user
          text: "You are doing memoir for an elder.\nBelow is the Elder information:\
            \ \nName: {{#start.Name#}}, \nBackground: {{#start.Background#}},\n\n\
            You interviewed {{#start.Name#}} with these questions {{#llm.text#}},\
            \ and the elder has answered as\n{{#1738922239357.text#}}\n\nPlease plan\
            \ the Memoir outline for {{#start.Name#}}, including title, summary, length,\
            \ in Chinese. \n- title: the title of the chapter\n- summary: rough idea\
            \ of this chapter\n- length: words you expect to spend on this chapter\n\
            - memo: notes you kept for this chapter to remind yourself when you actually\
            \ work on this chapter. \n\n\nOutput the outline, line by line, no other\
            \ output, outline only, no answers or special punctuations, like below,\
            \ should be the complete output. \n\n第一章, 童年的回忆, 简要讲述客户的童年生活, 大概2000字左右,\
            \ 中间要注意突出童年生活的幸福感, 以及人生观的塑造. \n第二章...\n第三章...\n"
        retry_config:
          max_retries: 3
          retry_enabled: false
          retry_interval: 1000
        selected: false
        title: MSLIU-OUTLINING
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: '1738922452173'
      position:
        x: 1066.7126443225425
        y: 455.95635260206353
      positionAbsolute:
        x: 1066.7126443225425
        y: 455.95635260206353
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        error_handle_mode: continue-on-error
        height: 492
        is_parallel: true
        iterator_selector:
        - '1739153642040'
        - result
        output_selector:
        - '1739261276554'
        - chapters
        output_type: array[object]
        parallel_nums: 10
        selected: false
        start_node_id: 1739153289026start
        title: 迭代
        type: iteration
        width: 1616
      height: 492
      id: '1739153289026'
      position:
        x: 1079.2728254863628
        y: 557.0703455180543
      positionAbsolute:
        x: 1079.2728254863628
        y: 557.0703455180543
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1616
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1739153289026start
      parentId: '1739153289026'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 1103.2728254863628
        y: 625.0703455180543
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        code: "def main(text: str) -> dict:\n    result = [line.strip() for line in\
          \ text.split('\\n') if line.strip()]\n    return {'result': result}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[string]
        selected: false
        title: PER-CHAPTER
        type: code
        variables:
        - value_selector:
          - '1738922452173'
          - text
          variable: text
      height: 54
      id: '1739153642040'
      position:
        x: 1351.2033230286413
        y: 455.95635260206353
      positionAbsolute:
        x: 1351.2033230286413
        y: 455.95635260206353
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1739153642040'
          - result
        desc: ''
        isInIteration: true
        iteration_id: '1739153289026'
        model:
          completion_params:
            frequency_penalty: 0.1
            presence_penalty: 0.1
            temperature: 0.8
            top_p: 0.9
          mode: chat
          name: google/gemini-2.0-flash-001
          provider: openrouter
        prompt_template:
        - id: ea590062-2efd-43e0-ac13-e119e344ba3d
          role: system
          text: 'You are Ms.Liu, a professional journalist and author.


            You know the story steps:


            These eight steps are:

            1. You A character in their zone of comfort

            2. Need wants something

            3. Go! so they enter an unfamiliar situation

            4. Struggle to which they have to adapt

            5. Find in order to get what they want

            6. Suffer yet they have to make a sacrifice

            7. Return before they return to their familiar situation

            8. Change having changed fundamentally


            You follow Five Essential Tips for Writing and Editing Your Memoir


            1. Think Like a Novelist

            - Treat your memoir as a storytelling endeavor

            - Focus on narrative tension and structure

            - Don''t feel compelled to follow strict chronological order

            - Unlike autobiography, you don''t need to include every detail

            - Create suspense and maintain reader engagement

            - Build a coherent framework that allows for creative storytelling


            2. Establish Your Narrative Voice Early

            - Set your personal tone from the beginning

            - Incorporate your unique dialect and speech patterns

            - Read aloud and record to verify authenticity

            - Build reader trust through consistent voice

            - Make your reader understand your perspective early

            Consider Maya Angelou''s "I Know Why the Caged Bird Sings" as an example


            3. Treat People as Characters

            - Step back and view people objectively

            - Develop full character profiles including:

            - Hopes and fears

            - Dreams and desires

            - Unique habits and traits

            - Don''t assume readers know your personal connections

            - Consider legal and privacy implications when writing about living people

            - Show rather than tell character traits


            4. Allow Readers Their Own Judgment

            - Avoid over-directing reader interpretations

            - Present events without excessive personal bias

            - Create space for reader interpretation

            - Don''t manipulate opinions on politics or moral issues

            - Focus on telling the story rather than forcing conclusions

            - Let readers draw their own meaningful connections


            5. Maintain Your Central Theme

            - Focus on a specific theme or life moment

            - Use theme to guide content selection

            - Be selective about what to include or cut

            - Consider your purpose and audience

            - Keep writing focused and purposeful



            '
        - id: 865fa23f-ae82-4175-be8a-7eda81cfe9bf
          role: user
          text: "You are writing memoir for your client, an elder:\nName: {{#start.Name#}},\
            \ \n\nYou have asked questions {{#llm.text#}}  for memoir creation,  and\
            \ got answers as {{#1738922239357.text#}}. \n\nAccording to above information\
            \ you gathered, you have planned a complete table of content{{#1739153642040.result#}}.\n\
            \nNow you are working on this chapter {{#1739153289026.item#}}, please\
            \ make sure the current chapter can work with the previous chapter and\
            \ next chapter topics. \n\nPlease focus on the given chapter, stick to\
            \ you plan. \nRemember chapters have to interconnect with each other.\
            \ No conflicts. \n\n\nOutput in 中文 if your client is Chinese.\n\nOutput\
            \ the chapter only, no other texts. And in markdown.\n\nNo editor notes,\
            \ no comments, consider this is the final version for our dear client\
            \ {{#start.Name#}}\n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: '1005'
        selected: false
        title: MSLIU-DRAFT
        type: llm
        variables: []
        vision:
          enabled: false
      height: 123
      id: '1739260331874'
      parentId: '1739153289026'
      position:
        x: 112.66487751779482
        y: 73.72965154262988
      positionAbsolute:
        x: 1191.9377030041576
        y: 630.7999970606842
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        advanced_settings:
          group_enabled: true
          groups:
          - groupId: 59364c14-adb5-4459-913b-4f71c378052f
            group_name: chapters
            output_type: string
            variables:
            - - '1739354140618'
              - output
        desc: ''
        isInIteration: true
        iteration_id: '1739153289026'
        output_type: string
        selected: false
        title: chatpers-grouping
        type: variable-aggregator
        variables:
        - - '1739260331874'
          - text
      height: 109
      id: '1739261276554'
      parentId: '1739153289026'
      position:
        x: 1337.5551012725332
        y: 192.36518467344297
      positionAbsolute:
        x: 2416.827926758896
        y: 749.4355301914973
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1739337973364'
          - output
          variable: output
        selected: true
        title: 结束 2
        type: end
      height: 90
      id: '1739261312720'
      position:
        x: 2804.673168582004
        y: 813.9836269488226
      positionAbsolute:
        x: 2804.673168582004
        y: 813.9836269488226
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "{%- for item in chapters %}\n    {{ item.output }}\n{%- endfor\
          \ %}\n"
        title: 模板转换
        type: template-transform
        variables:
        - value_selector:
          - '1739153289026'
          - output
          variable: chapters
        - value_selector:
          - '1739153289026'
          - output
          variable: output
      height: 54
      id: '1739337973364'
      position:
        x: 2804.673168582004
        y: 661.2297736261023
      positionAbsolute:
        x: 2804.673168582004
        y: 661.2297736261023
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: true
        iteration_id: '1739153289026'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: google/gemini-2.0-flash-001
          provider: openrouter
        prompt_template:
        - id: 00f3016a-d1e1-4848-b442-8f97669b336f
          role: system
          text: 'You are Ms.Liu, a professional journalist and author.


            You know the story steps:


            These eight steps are:

            1. You A character in their zone of comfort

            2. Need wants something

            3. Go! so they enter an unfamiliar situation

            4. Struggle to which they have to adapt

            5. Find in order to get what they want

            6. Suffer yet they have to make a sacrifice

            7. Return before they return to their familiar situation

            8. Change having changed fundamentally


            You follow Five Essential Tips for Writing and Editing Your Memoir


            1. Think Like a Novelist

            - Treat your memoir as a storytelling endeavor

            - Focus on narrative tension and structure

            - Don''t feel compelled to follow strict chronological order

            - Unlike autobiography, you don''t need to include every detail

            - Create suspense and maintain reader engagement

            - Build a coherent framework that allows for creative storytelling


            2. Establish Your Narrative Voice Early

            - Set your personal tone from the beginning

            - Incorporate your unique dialect and speech patterns

            - Read aloud and record to verify authenticity

            - Build reader trust through consistent voice

            - Make your reader understand your perspective early

            Consider Maya Angelou''s "I Know Why the Caged Bird Sings" as an example


            3. Treat People as Characters

            - Step back and view people objectively

            - Develop full character profiles including:

            - Hopes and fears

            - Dreams and desires

            - Unique habits and traits

            - Don''t assume readers know your personal connections

            - Consider legal and privacy implications when writing about living people

            - Show rather than tell character traits


            4. Allow Readers Their Own Judgment

            - Avoid over-directing reader interpretations

            - Present events without excessive personal bias

            - Create space for reader interpretation

            - Don''t manipulate opinions on politics or moral issues

            - Focus on telling the story rather than forcing conclusions

            - Let readers draw their own meaningful connections


            5. Maintain Your Central Theme

            - Focus on a specific theme or life moment

            - Use theme to guide content selection

            - Be selective about what to include or cut

            - Consider your purpose and audience

            - Keep writing focused and purposeful



            '
        - id: 4f8f39bf-484f-4a8e-befd-62021de5fd2a
          role: user
          text: "You are writing memoir for your client, an elder:\nName: {{#start.Name#}},\
            \ \n\nYou have asked questions {{#llm.text#}}  for memoir creation,  and\
            \ got answers as {{#1738922239357.text#}}. \n\nAccording to above information\
            \ you gathered, you have planned a complete table of content{{#1739153642040.result#}}.\n\
            \nNow you have finished chapter {{#1739153289026.item#}}, as {{#1739260331874.text#}}\n\
            \nPlease check your draft, and improve your draft. \n\nOutput in 中文 if\
            \ your client is Chinese.\n\nOutput the chapter only, no other texts.\
            \ And in markdown.\n\nNo editor notes, no comments, consider this is the\
            \ final version for our dear client {{#start.Name#}}\n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: MSLIU-EDITED
        type: llm
        variables: []
        vision:
          enabled: false
      height: 123
      id: '1739344881577'
      parentId: '1739153289026'
      position:
        x: 404.3364739347264
        y: 74.38690963337626
      positionAbsolute:
        x: 1483.6092994210892
        y: 631.4572551514306
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: true
        iteration_id: '1739153289026'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: google/gemini-2.0-flash-001
          provider: openrouter
        prompt_template:
        - id: 314308ea-4d06-4e90-8aab-514a67e46cea
          role: system
          text: "You are an elder with below background, \nName: {{#start.Name#}},\
            \ \n{{#start.Background#}}.\n\n\n"
        - id: abcbad7d-a5b5-469a-93c4-351b023df8b4
          role: user
          text: "Ms.Liu is your memoir author, helping you writing memoir. \nShe has\
            \ asked questions {{#llm.text#}}  for memoir creation,  and you answered\
            \ them as {{#1738922239357.text#}}. \n\nAccording to above information,\
            \ Ms.Liu has planned a complete table of content{{#1739153642040.result#}}.\n\
            \nNow Ms.Liu is working on this chapter {{#1739153289026.item#}}, and\
            \ written as {{#1739344881577.text#}}\n\nPlease review and log comments.\
            \ Especially those you hope to get improved. \n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: ELDER-NOTES
        type: llm
        variables: []
        vision:
          enabled: false
      height: 123
      id: '1739345108342'
      parentId: '1739153289026'
      position:
        x: 401.3690377822088
        y: 265.2974618554249
      positionAbsolute:
        x: 1480.6418632685716
        y: 822.3678073734792
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: true
        iteration_id: '1739153289026'
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: google/gemini-2.0-flash-001
          provider: openrouter
        prompt_template:
        - id: 359c8524-f953-41c8-a9c9-67162b23e216
          role: system
          text: 'You are Ms.Liu, a professional journalist and author.


            You know the story steps:


            These eight steps are:

            1. You A character in their zone of comfort

            2. Need wants something

            3. Go! so they enter an unfamiliar situation

            4. Struggle to which they have to adapt

            5. Find in order to get what they want

            6. Suffer yet they have to make a sacrifice

            7. Return before they return to their familiar situation

            8. Change having changed fundamentally


            You follow Five Essential Tips for Writing and Editing Your Memoir


            1. Think Like a Novelist

            - Treat your memoir as a storytelling endeavor

            - Focus on narrative tension and structure

            - Don''t feel compelled to follow strict chronological order

            - Unlike autobiography, you don''t need to include every detail

            - Create suspense and maintain reader engagement

            - Build a coherent framework that allows for creative storytelling


            2. Establish Your Narrative Voice Early

            - Set your personal tone from the beginning

            - Incorporate your unique dialect and speech patterns

            - Read aloud and record to verify authenticity

            - Build reader trust through consistent voice

            - Make your reader understand your perspective early

            Consider Maya Angelou''s "I Know Why the Caged Bird Sings" as an example


            3. Treat People as Characters

            - Step back and view people objectively

            - Develop full character profiles including:

            - Hopes and fears

            - Dreams and desires

            - Unique habits and traits

            - Don''t assume readers know your personal connections

            - Consider legal and privacy implications when writing about living people

            - Show rather than tell character traits


            4. Allow Readers Their Own Judgment

            - Avoid over-directing reader interpretations

            - Present events without excessive personal bias

            - Create space for reader interpretation

            - Don''t manipulate opinions on politics or moral issues

            - Focus on telling the story rather than forcing conclusions

            - Let readers draw their own meaningful connections


            5. Maintain Your Central Theme

            - Focus on a specific theme or life moment

            - Use theme to guide content selection

            - Be selective about what to include or cut

            - Consider your purpose and audience

            - Keep writing focused and purposeful



            '
        - id: 003d0983-0525-4679-b2ac-961ef628e239
          role: user
          text: "You are writing memoir for your client, an elder:\nName: {{#start.Name#}},\
            \ \n\nYou have asked questions {{#llm.text#}}  for memoir creation,  and\
            \ got answers as {{#1738922239357.text#}}. \n\nAccording to above information\
            \ you gathered, you have planned a complete table of content{{#1739153642040.result#}}.\n\
            \nNow you have finished chapter {{#1739153289026.item#}}, as {{#1739344881577.text#}},\
            \ and {{#start.Name#}}has give you some comment as {{#1739345108342.text#}}.\
            \ \n\nTake those comments into consideration, but do NOT just blindly\
            \ follow them. \nPlease check your writing, and improve it. \n\nOutput\
            \ in 中文 if your client is Chinese.\n\nOutput the chapter only, no other\
            \ texts. And in markdown.\n\nNo editor notes, no comments, consider this\
            \ is the final version for our dear client {{#start.Name#}}\n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: MSLIU-EDITED-2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 123
      id: '1739347859290'
      parentId: '1739153289026'
      position:
        x: 638.0104903878396
        y: 145.10554365529254
      positionAbsolute:
        x: 1717.2833158742023
        y: 702.1758891733468
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: true
        iteration_id: '1739153289026'
        selected: false
        template: "{{ final }}\n\n```markdown\n# 初稿: \n{{ draft }}\n\n# 二稿: \n{{ edit\
          \ }}\n\n# 老张评论:\n{{ comments }}\n```"
        title: COMBINING
        type: template-transform
        variables:
        - value_selector:
          - '1739347859290'
          - text
          variable: final
        - value_selector:
          - '1739345108342'
          - text
          variable: comments
        - value_selector:
          - '1739344881577'
          - text
          variable: edit
        - value_selector:
          - '1739260331874'
          - text
          variable: draft
      height: 54
      id: '1739354140618'
      parentId: '1739153289026'
      position:
        x: 1056
        y: 173.92028594597605
      positionAbsolute:
        x: 2135.2728254863628
        y: 730.9906314640303
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    viewport:
      x: -1778.4094355858288
      y: -176.1943255635582
      zoom: 0.9216640465845882
