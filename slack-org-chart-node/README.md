# Slack Organization Chart Generator

A Node.js script that generates an organizational chart in Org-mode format by fetching data from Slack's API.

## Features

- Fetches organizational hierarchy from Slack
- Converts data into Org-mode formatted text
- Configurable depth levels for hierarchy
- Search by email address
- Clean and readable output format

## Prerequisites

- Node.js (v14 or higher)
- Slack Workspace access
- Slack API token with appropriate permissions

## Installation

```bash
npm install
```

## Configuration

1. Create a `.env` file in the root directory
2. Add your Slack API token:

```
SLACK_BOT_TOKEN=xoxb-your-token-here
```

## Usage

```bash
node slack-org.js --email <EMAIL> --depth 3
```

### Parameters

- `--email`: Email address of the target employee (required)
- `--depth`: Number of organizational levels to include (default: 2)

### Output Example

```org
* Company Organization [2024-01-20]
** CEO - <PERSON>
   :PROPERTIES:
   :EMAIL: <EMAIL>
   :TITLE: Chief Executive Officer
   :END:
*** CTO - <PERSON>
    :PROPERTIES:
    :EMAIL: <EMAIL>
    :TITLE: Chief Technology Officer
    :END:
**** Engineering Manager - <PERSON>
     :PROPERTIES:
     :EMAIL: <EMAIL>
     :TITLE: Engineering Manager
     :DEPARTMENT: Engineering
     :END:
```

## Permissions Required

- `users:read`
- `users:read.email`

## Limitations

- Only works with Slack Enterprise Grid
- Requires users to have their reporting structure set up in Slack
- Maximum depth level is 5

## Error Handling

The script will display meaningful error messages for common issues:

- Invalid email address
- User not found
- API permission errors
- Network connectivity issues
