{"name": "slack-org-chart-node", "version": "1.0.0", "description": "A Node.js script that generates an organizational chart in Org-mode format by fetching data from Slack's API", "main": "slack-org.js", "scripts": {"start": "node slack-org.js"}, "keywords": ["slack", "organization-chart", "org-mode"], "dependencies": {"@slack/web-api": "^6.8.1", "dotenv": "^16.0.3", "yargs": "^17.7.1"}, "engines": {"node": ">=14.0.0"}}