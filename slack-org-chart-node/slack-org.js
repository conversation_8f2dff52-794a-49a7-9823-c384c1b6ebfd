require('dotenv').config();
const { WebClient } = require('@slack/web-api');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('email', {
    description: 'Email address of the target employee',
    type: 'string',
    demandOption: true
  })
  .option('depth', {
    description: 'Number of organizational levels to include',
    type: 'number',
    default: 2
  })
  .check((argv) => {
    if (argv.depth > 5) throw new Error('Maximum depth level is 5');
    if (argv.depth < 1) throw new Error('Minimum depth level is 1');
    return true;
  })
  .help()
  .argv;

// Initialize Slack Web Client
if (!process.env.SLACK_BOT_TOKEN) {
  console.error('Error: SLACK_BOT_TOKEN is not set in .env file');
  process.exit(1);
}

const slack = new WebClient(process.env.SLACK_BOT_TOKEN);
console.log('Initializing Slack Web Client...');

// Format date for org header
const getFormattedDate = () => {
  const date = new Date();
  return date.toISOString().split('T')[0];
};

// Generate org-mode properties block
const generateProperties = (user) => {
  const props = [];
  props.push(':PROPERTIES:');
  if (user.profile.email) props.push(`:EMAIL: ${user.profile.email}`);
  if (user.profile.title) props.push(`:TITLE: ${user.profile.title}`);
  if (user.profile.fields?.Department?.value) {
    props.push(`:DEPARTMENT: ${user.profile.fields.Department.value}`);
  }
  props.push(':END:');
  return props.join('\n');
};

// Generate org-mode heading with proper indentation
const generateHeading = (level, user) => {
  const stars = '*'.repeat(level);
  const name = user.profile.real_name || user.name;
  const title = user.profile.title ? ` - ${user.profile.title}` : '';
  return `${stars} ${name}${title}`;
};

// Fetch user info by email
async function getUserByEmail(email) {
  console.log(`Fetching user information for email: ${email}...`);
  try {
    const result = await slack.users.lookupByEmail({
      email: email
    });
    console.log('Successfully retrieved user information');
    return result.user;
  } catch (error) {
    if (error.code === 'not_authed') {
      console.error('Authentication Error: Invalid or missing Slack token');
      console.error('Please ensure you have:');
      console.error('1. Created a .env file in the root directory');
      console.error('2. Added a valid SLACK_BOT_TOKEN to the .env file');
      console.error('3. Given the bot proper OAuth scopes (users:read, users:read.email)');
    } else {
      console.error('Error fetching user:', error.message);
      console.error('Full error details:', error);
    }
    process.exit(1);
  }
}

// Build organizational chart recursively
async function buildOrgChart(userId, level = 1, maxDepth) {
  if (level > maxDepth) return [];

  console.log(`Building org chart for user ID: ${userId} at level ${level}...`);
  try {
    // Get user info
    const { user } = await slack.users.info({ user: userId });
    const lines = [];

    // Add user heading and properties
    lines.push(generateHeading(level, user));
    lines.push(generateProperties(user));

    // Get reports
    if (level < maxDepth) {
      const reports = await slack.users.list();
      const directReports = reports.members.filter(member => 
        member.profile.manager_id === userId
      );

      // Process each direct report
      for (const report of directReports) {
        const reportLines = await buildOrgChart(report.id, level + 1, maxDepth);
        lines.push(...reportLines);
      }
    }

    return lines;
  } catch (error) {
    console.error('Error building org chart:', error.message);
    process.exit(1);
  }
}

// Main function
async function main() {
  try {
    const user = await getUserByEmail(argv.email);
    const orgChart = ['* Company Organization [' + getFormattedDate() + ']'];
    const chart = await buildOrgChart(user.id, 2, argv.depth + 1);
    orgChart.push(...chart);
    
    console.log(orgChart.join('\n'));
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

main();