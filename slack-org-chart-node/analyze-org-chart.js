// Script to analyze organizational chart structure

// Calculate hierarchy level based on DOM nesting and position
function calculateHierarchyLevel (element) {
    let level = 0;
    let current = element;
    
    while (current.parentElement) {
        if (current.parentElement.classList.contains('p-rimeto_org_chart_group')) {
            level++;
        }
        current = current.parentElement;
    }
    
    return level;
};

// Find parent card based on position and DOM structure
function findParentCard (card, allCards) {
    const cardRect = card.getBoundingClientRect();
    let parentCard = null;
    let minDistance = Infinity;
    
    allCards.forEach((potentialParent) => {
        if (potentialParent === card) return;
        
        const parentRect = potentialParent.getBoundingClientRect();
        if (parentRect.bottom < cardRect.top) { // Parent should be above
            const verticalDistance = cardRect.top - parentRect.bottom;
            const horizontalOffset = Math.abs(cardRect.left - parentRect.left);
            
            // Calculate weighted distance (giving more importance to vertical alignment)
            const distance = verticalDistance + (horizontalOffset * 0.5);
            
            if (distance < minDistance) {
                minDistance = distance;
                parentCard = potentialParent;
            }
        }
    });
    
    return parentCard ? Array.from(allCards).indexOf(parentCard) : null;
};

// Print the organizational structure
function printOrgStructure (structure) {
    const printNode = (nodeId, level = 0) => {
        const node = structure.get(nodeId);
        console.log(
            '  '.repeat(level) +
            `${node.name} (${node.title}) - Level: ${node.level}, ` +
            `Position: (${Math.round(node.position.left)}, ${Math.round(node.position.top)})`
        );
        
        // Find and print children
        Array.from(structure.keys())
            .filter(id => structure.get(id).parent === nodeId)
            .forEach(childId => printNode(childId, level + 1));
    };
    
    // Start with root nodes (nodes with no parents)
    Array.from(structure.keys())
        .filter(id => structure.get(id).parent === null)
        .forEach(rootId => printNode(rootId));
};

function analyzeOrgChart () {
    // Get all org chart member cards
    const memberCards = document.querySelectorAll('button.p-rimeto_org_chart_member_card');
    
    // Create a map to store element positions and relationships
    const orgStructure = new Map();
    
    memberCards.forEach((card, index) => {
        // Get element's position
        const rect = card.getBoundingClientRect();
        
        // Get member info with improved selectors and error handling
        const name = card.querySelector('.p-rimeto_org_chart_member_card_name')?.textContent?.trim() || 
            card.getAttribute('aria-label')?.split(' ').slice(0, -2).join(' ') || 'Unknown';
        const title = card.querySelector('.p-rimeto_org_chart_member_card_title')?.textContent?.trim() || 
            card.getAttribute('aria-label')?.split(' ').slice(-2).join(' ') || 'No Title';
        
        // Get additional metadata from attributes
        const userId = card.getAttribute('data-user-id') || `user_${index}`;
        const orgLevel = parseInt(card.getAttribute('data-org-level')) || card.getAttribute('aria-level') || 0;
        
        // Get reports count
        const reportsCount = card.querySelector('.p-rimeto_org_chart_member_card_reports')?.textContent?.trim().match(/\d+/)?.[0] || '0';
        
        // Get avatar URL
        const avatarImg = card.querySelector('.p-rimeto_org_chart_member_card_avatar img');
        const avatarUrl = avatarImg ? avatarImg.getAttribute('src') : null;
        
        // Store element data with enhanced information
        const elementData = {
            id: index,
            userId,
            name,
            title,
            orgLevel,
            reportsCount: parseInt(reportsCount),
            avatarUrl,
            position: {
                top: rect.top,
                left: rect.left,
                width: rect.width,
                height: rect.height
            },
            level: calculateHierarchyLevel(card),
            parent: findParentCard(card, memberCards)
        };
        
        orgStructure.set(index, elementData);
    });
    
    // Print the structure
    console.log('Organizational Chart Structure:');
    printOrgStructure(orgStructure);
    return orgStructure;
};

// Execute the analysis
analyzeOrgChart();