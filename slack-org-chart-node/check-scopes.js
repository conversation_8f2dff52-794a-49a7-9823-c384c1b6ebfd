require('dotenv').config();
const fs = require('fs').promises;
const { WebClient } = require('@slack/web-api');

async function checkTokenScopes(tokenFilePath) {
    try {
        // Read tokens from file
        const tokens = (await fs.readFile(tokenFilePath, 'utf8'))
            .split('\n')
            .filter(token => token.trim() && token.startsWith('xoxb-'));

        console.log(`Found ${tokens.length} tokens to check...\n`);

        // Check each token
        for (const token of tokens) {
            const slack = new WebClient(token);
            try {
                // Test auth to get scopes
                const auth = await slack.auth.test();
                const { scopes } = await slack.auth.test();
                console.log(`Token: ${token.substring(0, 15)}...`);
                console.log('Team: ' + auth.team);
                console.log('Scopes:');
                if (scopes && Array.isArray(scopes)) {
                    scopes.forEach(scope => console.log(`  - ${scope}`));
                } else {
                    console.log('  No scopes found');
                }
                console.log('\n');
            } catch (error) {
                console.log(`Token: ${token.substring(0, 15)}...`);
                console.log(`Error: ${error.message}\n`);
            }
        }
    } catch (error) {
        if (error.code === 'ENOENT') {
            console.error('Error: tokens.txt file not found');
        } else {
            console.error('Error:', error.message);
        }
        process.exit(1);
    }
}

// Get tokens file path from command line or use default
const tokenFile = process.argv[2] || 'tokens.txt';
checkTokenScopes(tokenFile);