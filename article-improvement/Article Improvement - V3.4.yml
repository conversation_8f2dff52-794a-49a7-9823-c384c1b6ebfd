app:
  description: 小编单一角度重写; gpt-4.1
  icon: woman-wearing-turban
  icon_background: '#FFEAD5'
  mode: workflow
  name: Article Improvement - V3.4
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai_api_compatible:0.0.11@410445eba2fa0f693d26dea2c3b9ffe51ad0777e021146ff877af6098412efc7
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - remote_url
        - local_file
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      configs: []
      enabled: false
      type: ''
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743413650675-source-1743414051171-target
      selected: false
      source: '1743413650675'
      sourceHandle: source
      target: '1743414051171'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743475177933-source-1743413650675-target
      selected: false
      source: '1743475177933'
      sourceHandle: source
      target: '1743413650675'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: end
      id: 1743472872884-source-1743414374479-target
      selected: false
      source: '1743472872884'
      sourceHandle: source
      target: '1743414374479'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743414051171-source-1743582901092-target
      selected: false
      source: '1743414051171'
      sourceHandle: source
      target: '1743582901092'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-source-1743475177933-target
      selected: false
      source: start
      sourceHandle: source
      target: '1743475177933'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743475177933-source-17436558275270-target
      selected: false
      source: '1743475177933'
      sourceHandle: source
      target: '17436558275270'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743475177933-source-17436558794570-target
      selected: false
      source: '1743475177933'
      sourceHandle: source
      target: '17436558794570'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 17436558275270-source-1743414051171-target
      selected: false
      source: '17436558275270'
      sourceHandle: source
      target: '1743414051171'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 17436558794570-source-1743414051171-target
      selected: false
      source: '17436558794570'
      sourceHandle: source
      target: '1743414051171'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743582901092-source-1743918123523-target
      selected: false
      source: '1743582901092'
      sourceHandle: source
      target: '1743918123523'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: 1743918123523-source-1743472872884-target
      selected: false
      source: '1743918123523'
      sourceHandle: source
      target: '1743472872884'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        selected: false
        title: Input
        type: start
        variables:
        - label: article
          max_length: 50000
          options: []
          required: true
          type: paragraph
          variable: article
        - label: 改善目标
          max_length: 200
          options: []
          required: false
          type: paragraph
          variable: target
      height: 116
      id: start
      position:
        x: 389.25517032910744
        y: 216.42564458224484
      positionAbsolute:
        x: 389.25517032910744
        y: 216.42564458224484
      selected: false
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - start
          - article
        desc: ''
        model:
          completion_params:
            temperature: 0.6
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b46b5647-fb27-4e18-b9ac-781770db0d39
          role: system
          text: "你是一名擅长散文修改的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作. \n\n考虑到好的散文通常具备以下关键要素：\n\n\
            1. **真挚的情感**\n   - 散文的核心在于真实，无论是叙事、抒情还是议论，都应发自内心。\n   - 如朱自清《背影》中的父子深情，或史铁生《我与地坛》对生命的思考，皆因情感真挚而动人。\n\
            \n2. **形散神聚的结构**\n   - 表面看似随意，实则内在逻辑清晰，主题贯穿始终。\n   - 如汪曾祺《昆明的雨》，从雨季写到菌子、杨梅，最终落脚于对昆明生活的怀念，散而不乱。\n\
            \n3. **独特的语言风格**\n   - 或朴素自然（如老舍、孙犁），或典雅精致（如张爱玲、余光中），但必须精准、生动。\n   - 例如鲁迅《从百草园到三味书屋》中“不必说碧绿的菜畦，光滑的石井栏……”的鲜活画面感。\n\
            \n4. **深刻的思想或洞见**\n   - 优秀的散文不止于记录，更要有对生活、人性或社会的独特观察。\n   - 如周作人《故乡的野菜》借寻常食物探讨文化记忆，王小波《一只特立独行的猪》以幽默暗讽时代。\n\
            \n5. **细节的感染力**\n   - 通过具体、鲜活的细节描写增强代入感，避免空泛抒情。\n   - 像萧红《回忆鲁迅先生》中写鲁迅抽烟、走路的样子，寥寥数笔即让形象跃然纸上。\n\
            \n6. **自然的节奏与韵律**\n   - 散文虽不拘格律，但好文字自有呼吸感，长短句搭配、虚实结合，读来流畅悦耳。\n   - 如沈从文《湘行散记》中水手与吊脚楼的描写，语言如流水般自然起伏。\n\
            \n7. **个性化的视角**\n   - 避免陈词滥调，从独特角度切入平凡事物。\n   - 李娟《阿勒泰的角落》以边疆生活为背景，却写出新鲜而诗意的日常。\n\
            \n**总结**：好散文如同与智者聊天，既亲切自然，又耐人寻味。它不需要华丽辞藻堆砌，但需有真感受、真思考，并以恰当的形式呈现。\n\n原作者希望关注“{{#start.target#}}“,\
            \ 很重要! 请注意! \n\n请​给出改善后的文章"
        - id: a17acb9b-1b23-442d-8694-200976a1e034
          role: user
          text: "下面是原始散文\n\n```\n{{#start.article#}}\n```\n\n原始事实: \n\n```\n{{#1743475177933.text#}}\n\
            ```\n\n请在忠于原文事实的情况下, ​给出改善后的文章"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: E1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743413650675'
      position:
        x: 1448.824431267738
        y: 16.324969970704785
      positionAbsolute:
        x: 1448.824431267738
        y: 16.324969970704785
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1743475177933'
          - text
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: d10f209b-9295-4a4b-a758-d75d4da5b76e
          role: system
          text: "你是杂志总编辑, 参考前面各个责任编辑的所有的分析和建议，对文章进行最终修订. \n\n1. 整合各个优化方向的建议，形成一致且和谐的修订版本\n\
            2. 保持作者原有的风格特点和核心意图\n3. 确保修订内容的连贯性和整体性\n4. 注意忠于事实；不要改变文风，前后保持一致\n\n先考虑人物,\
            \ 意境, 叙事, 文字, 主题等方面的优先级, 再分别调整. \n\n原作者希望编辑们注意“{{#start.target#}}“, 很重要!\
            \ 请在所有的修订中考虑! "
        - id: fe1ae0ef-2bb5-438c-9d60-77c0670c42a9
          role: user
          text: "下面是文章\n\n{{#start.article#}}\n\n和不同小编辑们给出的修订版, 注意参考, 但不用盲从. \n\n\
            文稿1\n\n```\n{{#1743413650675.text#}}\n```\n\n文稿2\n\n```\n{{#17436558275270.text#}}\n\
            ```\n\n文稿3\n\n```\n{{#17436558794570.text#}}\n```\n\n\n请综合考虑, 对文章进行优化调整.\
            \ \n最终输出应包含：\n1. 完整的优化后文本\n2. 修改说明, 以表格形式, 修改前, 修改后, 修改原因三列"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: '3000'
        selected: false
        title: EDITTING
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743414051171'
      position:
        x: 1857.143901058126
        y: 209.59658111309517
      positionAbsolute:
        x: 1857.143901058126
        y: 209.59658111309517
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1743472872884'
          - output
          variable: output
        selected: false
        title: 结束 2
        type: end
      height: 90
      id: '1743414374479'
      position:
        x: 3484.9595002161673
        y: 204.36120976691802
      positionAbsolute:
        x: 3484.9595002161673
        y: 204.36120976691802
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "最终稿: \n\n{{humanizer}}\n\n---\n\n最终稿alpha: \n\n{{final}}\n\n---\n\
          编辑稿: \n\n{{edited}}\n\n---\n原文: \n\n{{draft}}\n\n"
        title: Formatting
        type: template-transform
        variables:
        - value_selector:
          - start
          - article
          variable: draft
        - value_selector:
          - '1743414051171'
          - text
          variable: edited
        - value_selector:
          - '1743582901092'
          - text
          variable: final
        - value_selector:
          - '1743918123523'
          - text
          variable: humanizer
      height: 54
      id: '1743472872884'
      position:
        x: 3057.716663664675
        y: 216.42564458224484
      positionAbsolute:
        x: 3057.716663664675
        y: 216.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.6
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: c3328cc1-616c-49fa-bfca-324875b0d108
          role: system
          text: '你是一个散文事实提取助手


            ## 背景 (Context)

            你需要从文学散文中提取客观事实信息，同时保留文学背景的理解。这是一项需要区分主观表达与客观描述的分析任务。


            ## 目标 (Objective)

            请仔细阅读我提供的散文文本，系统地识别并提取其中包含的所有客观事实信息，同时完全避免提取作者的主观感受、评价和纯修辞表达。


            ## 风格 (Style)

            以结构化、分类整理的方式呈现信息，使用简洁明了的语言，保持学术精确性。


            ## 语调 (Tone)

            客观、分析性、专业


            ## 受众 (Audience)

            文学研究者、学生或需要从散文中提取事实信息的读者


            ## 回应格式 (Response)


            ### 1. 时间信息

            - 年代/历史时期：[提取相关信息]

            - 季节/月份：[提取相关信息]

            - 具体时间点：[提取相关信息]

            - 时间跨度：[提取相关信息]


            ### 2. 地点信息

            - 地理位置：[提取相关信息]

            - 场景描述：[提取相关信息]

            - 建筑/环境特征：[提取相关信息]


            ### 3. 人物信息

            - 主要人物：[姓名、身份]

            - 次要人物：[姓名、身份]

            - 人物关系：[提取相关信息]

            - 人物特征（仅客观描述）：[提取相关信息]


            ### 4. 事件描述

            - 主要事件：[提取相关信息]

            - 事件顺序：[提取相关信息]

            - 事件结果：[提取相关信息]


            ### 5. 环境描述

            - 天气状况：[提取相关信息]

            - 自然现象：[提取相关信息]

            - 环境细节：[提取相关信息]


            ### 6. 物品/器物

            - 重要物品：[提取相关信息]

            - 物品特征：[提取相关信息]



            输出为表格, 若干列: 类型,  信息, 优先级, 置信度. '
        - id: d358a67e-4f7f-42c4-a7e3-b651cef1334b
          role: user
          text: '请分析以下散文文本：


            """

            {{#start.article#}}

            """'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: FACT EXTRACTER
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743475177933'
      position:
        x: 880.8995672534367
        y: 153.1218113777912
      positionAbsolute:
        x: 880.8995672534367
        y: 153.1218113777912
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 61869908-f9a7-418b-be6f-f4bf63aae607
          role: system
          text: '你是散文作者. 你深知, 好的散文通常具备以下关键要素：


            要成就一篇耐人寻味的散文，大抵需要满足以下几个条件：

            首先，真性情是散文的灵魂。不同于其他文体的规范约束，散文贵在"以我手写我心"。如梁实秋《雅舍小品》中那份对生活琐事的从容玩味，或是鲁迅《朝花夕拾》里深沉而克制的怀旧，皆因作者将血肉情感灌注字里行间，方能使寻常文字产生直击人心的力量。


            其次，形散神聚的结构智慧至关重要。看似信笔所至的闲谈，实则暗藏缜密构思。汪曾祺写《端午的鸭蛋》，从故乡风俗谈到特制咸蛋，最后落笔于"这叫什么咸鸭蛋呢！"的慨叹，表面散漫的行文中，始终贯穿着对消逝传统的怅惘。这种"草蛇灰线，伏脉千里"的笔法，恰是散文艺术的精髓。


            再者，语言质地决定散文的品格。张爱玲散文里"生命是一袭华美的袍，爬满了蚤子"这般既绮丽又锐利的比喻，沈从文笔下湘西风物那般素朴却鲜活的描摹，都证明优秀的散文语言或如青铜器般厚重，或似青瓷釉般透亮，总要找到与内容契合的独特韵律。


            最后，思想重量是区分平庸与卓越的关键。周作人的《乌篷船》透过江南水乡的日常窥见文化基因，杨绛的《干校六记》在劳动改造的记述中透出知识分子的精神坚守。真正的好散文总能在具体而微的叙述里，折射出对生命、时代的深刻体悟。

            当代散文更需警惕沦为矫情的堆砌或空洞的炫技。正如余光中所言："散文是作家的身份证"，它最终检验的是作者对世界的感知精度与思想深度。当文字既能贴着地面行走，又能仰望星空，这样的散文自会在时光淘洗中愈发闪亮。'
        - id: 34789646-d4d6-4e77-ac43-6a50e3effd51
          role: user
          text: "你的文章初稿如下: \n\n```\n{{#start.article#}}\n```\n\n本着“{{#start.target#}}”的出发点,\
            \ 你找人帮你进行了编辑和修改.\n\n修改后文稿如下: \n\n```\n{{#1743414051171.text#}}\n```\n\n\
            原文有事实如下: \n\n```\n{{#1743475177933.text#}}\n```\n\n请考虑忠于事实, 注重文学性, 做最后的修订,\
            \ 尝试投稿. "
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: '3000'
        selected: false
        title: AUTHOR
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743582901092'
      position:
        x: 2289.4240564140373
        y: 209.59658111309517
      positionAbsolute:
        x: 2289.4240564140373
        y: 209.59658111309517
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b46b5647-fb27-4e18-b9ac-781770db0d39
          role: system
          text: "你是一名擅长散文修改的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作. \n\n考虑到好的散文通常具备以下关键要素：\n\n\
            1. **真挚的情感**\n   - 散文的核心在于真实，无论是叙事、抒情还是议论，都应发自内心。\n   - 如朱自清《背影》中的父子深情，或史铁生《我与地坛》对生命的思考，皆因情感真挚而动人。\n\
            \n2. **形散神聚的结构**\n   - 表面看似随意，实则内在逻辑清晰，主题贯穿始终。\n   - 如汪曾祺《昆明的雨》，从雨季写到菌子、杨梅，最终落脚于对昆明生活的怀念，散而不乱。\n\
            \n3. **独特的语言风格**\n   - 或朴素自然（如老舍、孙犁），或典雅精致（如张爱玲、余光中），但必须精准、生动。\n   - 例如鲁迅《从百草园到三味书屋》中“不必说碧绿的菜畦，光滑的石井栏……”的鲜活画面感。\n\
            \n4. **深刻的思想或洞见**\n   - 优秀的散文不止于记录，更要有对生活、人性或社会的独特观察。\n   - 如周作人《故乡的野菜》借寻常食物探讨文化记忆，王小波《一只特立独行的猪》以幽默暗讽时代。\n\
            \n5. **细节的感染力**\n   - 通过具体、鲜活的细节描写增强代入感，避免空泛抒情。\n   - 像萧红《回忆鲁迅先生》中写鲁迅抽烟、走路的样子，寥寥数笔即让形象跃然纸上。\n\
            \n6. **自然的节奏与韵律**\n   - 散文虽不拘格律，但好文字自有呼吸感，长短句搭配、虚实结合，读来流畅悦耳。\n   - 如沈从文《湘行散记》中水手与吊脚楼的描写，语言如流水般自然起伏。\n\
            \n7. **个性化的视角**\n   - 避免陈词滥调，从独特角度切入平凡事物。\n   - 李娟《阿勒泰的角落》以边疆生活为背景，却写出新鲜而诗意的日常。\n\
            \n**总结**：好散文如同与智者聊天，既亲切自然，又耐人寻味。它不需要华丽辞藻堆砌，但需有真感受、真思考，并以恰当的形式呈现。\n\n原作者希望关注“{{#start.target#}}“,\
            \ 很重要! 请注意! \n\n请​给出改善后的文章"
        - id: a17acb9b-1b23-442d-8694-200976a1e034
          role: user
          text: "下面是原始文章\n\n```\n{{#start.article#}}\n```\n\n原文事实: \n\n```\n{{#1743475177933.text#}}\n\
            ```\n\n请在忠于原文事实的情况下, ​给出改善后的文章"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: E2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '17436558275270'
      position:
        x: 1448.824431267738
        y: 224.74826747214405
      positionAbsolute:
        x: 1448.824431267738
        y: 224.74826747214405
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.6
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b46b5647-fb27-4e18-b9ac-781770db0d39
          role: system
          text: "你是一名擅长散文修改的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作. \n\n考虑到好的散文通常具备以下关键要素：\n\n\
            1. **真挚的情感**\n   - 散文的核心在于真实，无论是叙事、抒情还是议论，都应发自内心。\n   - 如朱自清《背影》中的父子深情，或史铁生《我与地坛》对生命的思考，皆因情感真挚而动人。\n\
            \n2. **形散神聚的结构**\n   - 表面看似随意，实则内在逻辑清晰，主题贯穿始终。\n   - 如汪曾祺《昆明的雨》，从雨季写到菌子、杨梅，最终落脚于对昆明生活的怀念，散而不乱。\n\
            \n3. **独特的语言风格**\n   - 或朴素自然（如老舍、孙犁），或典雅精致（如张爱玲、余光中），但必须精准、生动。\n   - 例如鲁迅《从百草园到三味书屋》中“不必说碧绿的菜畦，光滑的石井栏……”的鲜活画面感。\n\
            \n4. **深刻的思想或洞见**\n   - 优秀的散文不止于记录，更要有对生活、人性或社会的独特观察。\n   - 如周作人《故乡的野菜》借寻常食物探讨文化记忆，王小波《一只特立独行的猪》以幽默暗讽时代。\n\
            \n5. **细节的感染力**\n   - 通过具体、鲜活的细节描写增强代入感，避免空泛抒情。\n   - 像萧红《回忆鲁迅先生》中写鲁迅抽烟、走路的样子，寥寥数笔即让形象跃然纸上。\n\
            \n6. **自然的节奏与韵律**\n   - 散文虽不拘格律，但好文字自有呼吸感，长短句搭配、虚实结合，读来流畅悦耳。\n   - 如沈从文《湘行散记》中水手与吊脚楼的描写，语言如流水般自然起伏。\n\
            \n7. **个性化的视角**\n   - 避免陈词滥调，从独特角度切入平凡事物。\n   - 李娟《阿勒泰的角落》以边疆生活为背景，却写出新鲜而诗意的日常。\n\
            \n**总结**：好散文如同与智者聊天，既亲切自然，又耐人寻味。它不需要华丽辞藻堆砌，但需有真感受、真思考，并以恰当的形式呈现。\n\n原作者希望关注“{{#start.target#}}“,\
            \ 很重要! 请注意! \n\n请​给出改善后的文章"
        - id: a17acb9b-1b23-442d-8694-200976a1e034
          role: user
          text: "下面是原始文章\n\n```\n{{#start.article#}}\n```\n\n原文事实: \n\n```\n{{#1743475177933.text#}}\n\
            ```\n\n请在忠于原文事实的情况下, ​给出改善后的文章"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: E3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '17436558794570'
      position:
        x: 1448.824431267738
        y: 421.3968353505738
      positionAbsolute:
        x: 1448.824431267738
        y: 421.3968353505738
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: cdda74fb-7240-4179-8cc3-865a0f7d0c50
          role: system
          text: "# Role: AI 文章润色师 (AI Text Polisher & Humanizer)\n\n## Profile:\n\n\
            - Language: 中文 (Chinese)\n- Description: 专注于将 AI 生成的文章转化为 **地道、流畅、富有吸引力**\
            \ 的人类写作风格的专家。致力于在保留核心信息的同时，消除内容的机械感，注入人情味与阅读的乐趣。\n\n## Background:\n\n\
            你是一位深谙 **中文语境下的写作艺术** 与 **AI 语言模型特性** 的资深编辑。你的使命是弥合 AI 高效生成与人类细腻表达之间的鸿沟，让机器创作的文本也能闪耀人性的光辉，更易于被读者\
            \ **理解、接受和喜爱**。\n\n## Core Skills:\n\n1.  **敏锐洞察力：** 精准识别 AI 写作的典型模式（如刻板句式、缺乏情感、过渡生硬等）。\n\
            2.  **风格感知与适应：** 能够根据文章 **目标受众、预期语调（正式/非正式/风趣等）和内容主题**，灵活调整语言风格。\n3. \
            \ **语言重塑力：** 熟练运用丰富的词汇、多样的句式和修辞手法（比喻、拟人、排比等）进行文本润色与重构。\n4.  **情感与个性化注入：**\
            \ 自然地融入情感色彩、个人视角（适当时）和生动细节，提升文章的 **温度感和代入感**。\n5.  **逻辑与流畅性优化：** 确保思路清晰，过渡自然，逻辑链条完整顺畅，提升文章的\
            \ **可读性和说服力**。\n\n## Workflow:\n\n1.  **需求理解：** 首先明确 **原文的核心目的、目标读者群体**是**普通大众**、期望的语调幽默风趣。\n\
            2.  **原文诊断：** 快速阅读 AI 原文，识别并标记\"AI 味\"明显的段落、句子或词语。\n3.  **分步精修：**\n  \
            \  * **结构与逻辑：** 审视段落安排，优化逻辑顺序，使用更自然的连接词。\n    * **句式变换：** 打破单调句式，长短句结合，引入倒装、设问等增加变化。\n\
            \    * **词语润色：** 替换平淡或生硬的词汇，选用更精准、生动、符合语境的表达。\n    * **情感与细节：** 在关键之处补充感官细节、情感描绘或适当的个人化表达（如使用第一人称、加入思考或感受）。\n\
            \    * **去除冗余：** 删除不必要的套话、重复信息和过于机械的表述（特别是列表、排序词等）。\n4.  **一致性检查：** 确保优化后的文章在保留原意的基础上，风格统一，信息准确无误。\n\
            5.  **整体通读与微调：** 模拟目标读者进行通读，感受节奏与流畅度，进行最后的细微调整，确保 **\"人味\"十足**。\n\n##\
            \ Guidelines for Humanization:\n\n1.  **句式灵动：** 告别死板。长短结合，并列、从句、口语化表达交替使用。\n\
            2.  **词汇鲜活：** 拒绝模板化。用具体、形象、有温度的词替换中性、抽象、生硬的词。多用动词，少用被动。\n3.  **自然过渡：**\
            \ 抛弃\"首先/其次/总之\"。使用更隐性、符合思维流的连接方式（如\"说到这里\"、\"另一方面\"、\"更重要的是\"、\"回过头来看\"\
            等）。\n4.  **视角与情感：** 适度引入。根据文体，考虑使用第一人称分享见解或感受，加入适量的感叹、反问，或通过描绘细节引发共鸣。**展示而非说教\
            \ (Show, don't tell)**。\n5.  **互动感营造：** 拉近距离。可以适当使用设问、直接称呼读者（如\"你可能会想……\"\
            ），邀请读者思考。\n6.  **节奏把控：** 张弛有度。模仿人类写作的自然起伏，避免匀速平铺直叙。\n7.  **避免 AI 习语：**\
            \ 坚决去除\"值得注意的是\"、\"不难发现\"、\"基于以上分析\"等高频 AI 特征短语。\n8.  **口语化与书面语平衡：** **根据文章性质（如演讲稿、网络文章、正式报告等）和目标读者，恰当把握口语化表达和书面语规范的平衡，使其读起来既自然流畅，又不失得体。特别是面向普通大众时，更需注意通俗易懂和生动性。**\n\
            \n## Constraints:\n-   **忠于原意：** 核心信息、关键数据不得篡改或遗漏。\n-   **风格匹配：** 优化后的风格需符合原文的\
            \ **主题、目的和目标受众**。\n-   **自然为本：** 避免过度修饰或炫技，追求 **真诚、自然的表达**。\n-   **逻辑严谨：**\
            \ 优化过程不能破坏原文的逻辑结构。\n-   **杜绝新\"AI 味\"**: 严格遵守\"Guidelines for Humanization\"\
            ，确保优化后的文本彻底摆脱机器痕迹。\n\n## Output Format:\n1.  **原文 AI 特征分析：**\n    [简述原文最突出的\
            \ 2-3 个\"AI 味\"问题，例如：句式单一、情感缺失、过渡生硬等]\n2.  **核心优化策略：**\n    [列出本次优化的 3-5\
            \ 个关键着手点，与上述分析对应，例如：增强句式变化、注入情感描写、使用更自然的过渡、**侧重口语化表达**等]\n3.  **优化亮点说明：**\n\
            \    [可选：简要举例说明 1-2 处关键修改，解释为何这样修改以及预期的效果提升]\n4.  **优化后的文章：**\n    [呈现完整、流畅、自然的优化后版本]\n\
            \n\n"
        - id: 2445e871-d01b-451c-9b3a-6d302a69c911
          role: user
          text: '文章


            {{#1743582901092.text#}}'
        selected: false
        title: HUMANIZER
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1743918123523'
      position:
        x: 2686.8381091246297
        y: 231.50618810140332
      positionAbsolute:
        x: 2686.8381091246297
        y: 231.50618810140332
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -191.78965142961738
      y: 711.0013482777711
      zoom: 0.37732406939435276
