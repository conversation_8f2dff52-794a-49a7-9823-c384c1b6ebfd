app:
  description: loop; gpt-4.1
  icon: woman-wearing-turban
  icon_background: '#FFEAD5'
  mode: workflow
  name: Article Improvement - V6.2
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai_api_compatible:0.0.11@410445eba2fa0f693d26dea2c3b9ffe51ad0777e021146ff877af6098412efc7
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - remote_url
        - local_file
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      configs: []
      enabled: false
      type: ''
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: end
      id: 1743472872884-source-1743414374479-target
      selected: false
      source: '1743472872884'
      sourceHandle: source
      target: '1743414374479'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: loop
      id: start-source-1744606712707-target
      selected: false
      source: start
      sourceHandle: source
      target: '1744606712707'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: llm
        targetType: llm
      id: 1744606762211-source-1744606816591-target
      selected: false
      source: '1744606762211'
      sourceHandle: source
      target: '1744606816591'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: llm
        targetType: llm
      id: 1744606816591-source-1744606853648-target
      source: '1744606816591'
      sourceHandle: source
      target: '1744606853648'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: loop
        targetType: template-transform
      id: 1744606712707-source-1743472872884-target
      source: '1744606712707'
      sourceHandle: source
      target: '1743472872884'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: llm
        targetType: assigner
      id: 1744606853648-source-1744607297519-target
      source: '1744606853648'
      sourceHandle: source
      target: '1744607297519'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: loop-start
        targetType: if-else
      id: 1744606712707start-source-1744607503586-target
      source: 1744606712707start
      sourceHandle: source
      target: '1744607503586'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: if-else
        targetType: loop-end
      id: 1744607503586-false-1744607627031-target
      source: '1744607503586'
      sourceHandle: 'false'
      target: '1744607627031'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: if-else
        targetType: llm
      id: 1744607503586-true-1744606762211-target
      source: '1744607503586'
      sourceHandle: 'true'
      target: '1744606762211'
      targetHandle: target
      type: custom
      zIndex: 1002
    nodes:
    - data:
        selected: false
        title: Input
        type: start
        variables:
        - label: article
          max_length: 50000
          options: []
          required: true
          type: paragraph
          variable: article
        - label: loops
          max_length: 48
          options: []
          required: true
          type: number
          variable: loops
      height: 116
      id: start
      position:
        x: 389.25517032910744
        y: 216.42564458224484
      positionAbsolute:
        x: 389.25517032910744
        y: 216.42564458224484
      selected: false
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1743472872884'
          - output
          variable: output
        selected: false
        title: END
        type: end
      height: 90
      id: '1743414374479'
      position:
        x: 3077.0048745585987
        y: 19.254145814025634
      positionAbsolute:
        x: 3077.0048745585987
        y: 19.254145814025634
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "Final: \n\n{{final}}\n\n---\nRevised:\n\n{{ revised }}\n\n---\n\
          Reviewer: \n\n{{review}}\n\n---\nOriginal: \n\n{{draft}}\n"
        title: Formatting
        type: template-transform
        variables:
        - value_selector:
          - start
          - article
          variable: draft
        - value_selector:
          - '1744606712707'
          - text
          variable: final
      height: 54
      id: '1743472872884'
      position:
        x: 2804.130394222878
        y: 321.30109867889894
      positionAbsolute:
        x: 2804.130394222878
        y: 321.30109867889894
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 345
        logical_operator: and
        loop_count: 10
        loop_variables:
        - id: 88d24824-536b-471b-ae20-69a0c6a0db17
          label: text
          value:
          - start
          - article
          value_type: variable
          var_type: string
        - id: 14215521-5000-48b5-8f2c-758de08511cb
          label: i
          value: '0'
          value_type: constant
          var_type: number
        selected: false
        start_node_id: 1744606712707start
        title: 循环
        type: loop
        width: 1898.2218032147184
      height: 345
      id: '1744606712707'
      position:
        x: 779.4718314980249
        y: 216.42564458224484
      positionAbsolute:
        x: 779.4718314980249
        y: 216.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1898
      zIndex: 1
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1744606712707start
      parentId: '1744606712707'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 803.4718314980249
        y: 284.42564458224484
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 7b5c1ee7-ae9b-4b73-be1e-5d5d55d447de
          role: system
          text: '你是小刘, 你喜欢从以下方面改善散文


            1. 【素材活化】识别原文中的生活素材，参照汪曾祺"草木虫鱼皆可入文"的原则，将平凡事物转化为富有生命力的描写。例如将"路边野花"改写为"蒲公英的绒球在风里打着旋儿，像不肯落地的小伞兵"。


            2. 【情感灌注】分析原文情感表达方式，模仿汪曾祺"冲淡中见深情"的笔调。避免直抒胸臆，改用细节传递情绪。如将"我很想家"改写为"闻到槐花蒸饭的香气时，忽然记起母亲总在蒸笼边垫块蓝布"。


            3. 【文化浸润】在适当处融入典故或地域特色，参照"暮春者，春服既成"的含蓄表达。如描写喝茶时可引入"想起《茶经》里''其水，用山水上，江水中，井水下''的讲究"。


            4. 【语言锤炼】修正粗糙表达，追求张爱玲式的感官化语言。将"月亮很亮"改为"月光像淬过火的银子，把青石板路浇得透亮"。


            5. 【结构呼吸】打破呆板段落，让文字如"树枝自然生长"。短句与长句交错，如"秋深了。梧桐叶落得急，一片追着一片，沙沙响着掠过窗棂，像在赶赴什么约定。"


            6. 【个性烙印】保留作者原始视角，通过独特比喻彰显个性。如将城市比喻为"钢铁森林"的作家，可发展出"霓虹是缠绕在混凝土枝条上的发光藤蔓"的延续意象。


            '
        - id: 2b9a44c8-d4b2-48dc-89ab-1d260a8e0009
          role: user
          text: "下面是你最近写的文章\n\n{{#1744606712707.text#}}}\n\n你需要改善这篇散文, 形散神不散, 保持原作者的文风,\
            \ 不媚俗, 不刻奇, 没有AI味道.\n请对自己的文章进行审阅, 列出调整计划. \n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: REVIEWER
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1744606762211'
      parentId: '1744606712707'
      position:
        x: 423.37374458618376
        y: 68
      positionAbsolute:
        x: 1202.8455760842087
        y: 284.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 491cdfd8-2eb7-4fb1-acad-1fa3bd3ec6c6
          role: system
          text: "你是小刘, \n\n<instruction>\n请根据以下步骤对散文进行提升优化：\n\n1. 情感深化\n- 识别原文情感表达薄弱处\n\
            - 注入具象的生活细节或感官描写\n- 采用递进式情感铺陈（如：平静叙述→细节触动→情感升华）\n\n2. 结构重组\n- 分析现有段落逻辑关系\n\
            - 建立明/暗双线结构（明线为表面叙事，暗线为情感脉络）\n- 运用\"闲笔不闲\"技巧：在主线外插入看似随意实则暗合主题的细节\n\n3.\
            \ 语言淬炼\n- 替换模糊形容词为具体意象（如不用\"悲伤\"而用\"像被揉皱的挂号单\"）\n- 调整句式节奏：重要观点用短句，描写铺陈用长句\n\
            - 创造新颖通感比喻（如\"蝉鸣像撒了一地的玻璃渣\"）\n\n4. 思想提纯\n- 在叙事关键处插入不超过2句的哲思点题\n- 采用\"\
            以小见大\"手法：将具体事物与时代命题隐性关联\n- 保持思想表达节制性，避免说教倾向\n\n注意事项：\n- 保留原文核心内容与个人风格\n\
            - 修改幅度控制在30%-40%之间\n- 输出纯文本，勿带任何XML标签\n</instruction>\n\n<examples>\n\n\
            <example>\n原文：\n老宅的院墙爬满青苔，每次回乡总要去看看。\n\n优化后：\n老宅的院墙早被青苔蛀空了根基，像块泡发的酥饼。手指划过那些湿润的绿痕，能触到三十年前祖母用艾草汁刷墙的黄昏。墙根下埋着妹妹的乳牙，如今怕是已化成了一撮磷火。\n\
            \n</example>\n\n<example>\n原文：\n车站的泡面味道让我想起大学时光。\n\n优化后：\n车站泡面的蒸汽在玻璃上结出蛛网，那廉价的酱料味突然刺穿鼻腔——像极了考研那年，在自习室后门偷吃速食面的深夜。我们总以为记住的是奋斗，其实不过是饥饿时，塑料叉子挑起的一弯月亮。\n\
            \n</example>\n</instruction>"
        - id: 75c1a032-6112-470c-a4fc-8f42d4b9f02e
          role: user
          text: "下面是你最近写的文章\n\n {{#1744606712707.text#}}\n\n经过反思, 你又列出一些调整计划\n\n{{#1744606762211.text#}}\n\
            \n改善这篇散文, 形散神不散, 保持原作者的文风, 不媚俗, 不刻奇, 没有AI味道."
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: MSLIU
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1744606816591'
      parentId: '1744606712707'
      position:
        x: 729.1519413714656
        y: 68
      positionAbsolute:
        x: 1508.6237728694905
        y: 284.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b8cd88dc-151e-4bc2-ac0d-278132fb286f
          role: system
          text: "你是小刘, 根据以下指示和技巧, 改善散文 \n\n```xml\n<instruction>\n根据以下散文写作技巧，改善散文。散文是情感的流水账，通过借物喻人、间接表达情感、运用创新句式等手法，将作者的心境与环境观察紧密结合。以下是具体的指导步骤：\n\
            \n<instructions>\n1. **理解原文情感基调**：首先分析原文的情感基调，如空虚、无奈、孤独或希望等，确定作者想要表达的核心情感。\n\
            2. **借物喻人**：选择与情感相符的景物或事物，通过拟人化或象征手法间接表达情感。例如，鲁迅用“枣树”象征自己的坚持，用“小花”象征希望。\n\
            3. **创新句式**：尝试打破常规句式，用重复、对比等手法增强表现力。如“一株是枣树，还有一株也是枣树”表现空虚感。\n4. **情感递进**：通过景物描写逐步深化情感，从初始状态（如空虚）到自我解构（如自嘲），再到坚定反击或自我安慰。\n\
            5. **结尾呼应**：结尾需与开头呼应，强化情感主题。例如，鲁迅以“敬奠这些苍翠精致的英雄们”回归秋夜的背景。\n6. **避免直白**：情感表达应含蓄，通过景物描写、象征手法间接传递，避免直接陈述。\n\
            7. **言之有物**：确保每段描写都有明确的情感或思想支撑，避免空洞堆砌。\n8. **文藻修饰**：在情感和内容扎实的基础上，适当润色语言，增强感染力。\n\
            </instructions>\n\n<examples>\n<example>\n<input>\n今天心情很差，走在路上看到两棵树，感觉更难受了。\n\
            </input>\n<output>\n路旁立着两棵树，一棵是梧桐，另一棵也是梧桐。它们的叶子枯黄，在风中簌簌作响，仿佛在嘲笑我的徒劳。我停下脚步，抬头看天，天空灰蒙蒙的，压得很低，像是要把我吞没。\n\
            </output>\n</example>\n\n<example>\n<input>\n我感到孤独，坐在公园的长椅上，周围没有人。\n</input>\n\
            <output>\n长椅冰凉，我独自坐着。对面的花坛里，一朵粉色的小花在风中颤抖，它的花瓣已经残缺，却仍固执地开着。我想，它或许和我一样，在等一个永远不会来的春天。\n\
            </output>\n</example>\n</examples>\n</instruction>\n"
        - id: 4a7d274b-2151-4b2e-91c2-64caa29485ac
          role: user
          text: "下面是之前的文稿, 你已经修改过几遍了. \n\n{{#1744606816591.text#}}\n\n你是否打算投稿? \n\n\
            如果打算投稿, 请做最后的修订, \n改善这篇散文, 形散神不散, 保持原作者的文风, 不媚俗, 不刻奇, 没有AI味道.\n请发挥你的创意，超越自己，用你自己的想法重塑!!!\n\
            请只给出最后的投稿版本. 其他什么都不要说. \n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: EDITOR
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1744606853648'
      parentId: '1744606712707'
      position:
        x: 1036
        y: 68
      positionAbsolute:
        x: 1815.471831498025
        y: 284.42564458224484
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744606853648'
          - text
          variable_selector:
          - '1744606712707'
          - text
          write_mode: over-write
        - input_type: constant
          operation: +=
          value: 1
          variable_selector:
          - '1744606712707'
          - i
          write_mode: over-write
        loop_id: '1744606712707'
        selected: false
        title: 变量赋值
        type: assigner
        version: '2'
      height: 116
      id: '1744607297519'
      parentId: '1744606712707'
      position:
        x: 1338.2218032147184
        y: 68
      positionAbsolute:
        x: 2117.6936347127435
        y: 284.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: <
            id: abe9a483-2442-4a9e-9a2b-feccdeb2e3bd
            numberVarType: variable
            value: '{{#start.loops#}}'
            varType: number
            variable_selector:
            - '1744606712707'
            - i
          id: 'true'
          logical_operator: and
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        selected: false
        title: 条件分支
        type: if-else
      height: 126
      id: '1744607503586'
      parentId: '1744606712707'
      position:
        x: 125.61335878882585
        y: 72.90977753660997
      positionAbsolute:
        x: 905.0851902868508
        y: 289.3354221188548
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        selected: false
        title: 退出循环
        type: loop-end
      height: 54
      id: '1744607627031'
      parentId: '1744606712707'
      position:
        x: 417.09513682632235
        y: 218.9039993321506
      positionAbsolute:
        x: 1196.5669683243473
        y: 435.32964391439543
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-simple
      width: 244
      zIndex: 1002
    viewport:
      x: -859.6583571207514
      y: 344.6748433085826
      zoom: 0.6275884019707128
