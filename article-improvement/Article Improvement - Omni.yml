app:
  description: Omni;gpt-4.1
  icon: woman-wearing-turban
  icon_background: '#FFEAD5'
  mode: workflow
  name: Article Improvement - Omni
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai_api_compatible:0.0.11@410445eba2fa0f693d26dea2c3b9ffe51ad0777e021146ff877af6098412efc7
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - remote_url
        - local_file
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      configs: []
      enabled: false
      type: ''
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743413650675-source-1743414051171-target
      selected: false
      source: '1743413650675'
      sourceHandle: source
      target: '1743414051171'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743475177933-source-1743413650675-target
      selected: false
      source: '1743475177933'
      sourceHandle: source
      target: '1743413650675'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: end
      id: 1743472872884-source-1743414374479-target
      selected: false
      source: '1743472872884'
      sourceHandle: source
      target: '1743414374479'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743414051171-source-1743582901092-target
      selected: false
      source: '1743414051171'
      sourceHandle: source
      target: '1743582901092'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-source-1743475177933-target
      selected: false
      source: start
      sourceHandle: source
      target: '1743475177933'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743475177933-source-17436558275270-target
      selected: false
      source: '1743475177933'
      sourceHandle: source
      target: '17436558275270'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743475177933-source-17436558794570-target
      selected: false
      source: '1743475177933'
      sourceHandle: source
      target: '17436558794570'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 17436558275270-source-1743414051171-target
      selected: false
      source: '17436558275270'
      sourceHandle: source
      target: '1743414051171'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 17436558794570-source-1743414051171-target
      selected: false
      source: '17436558794570'
      sourceHandle: source
      target: '1743414051171'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743582901092-source-1743918123523-target
      selected: false
      source: '1743582901092'
      sourceHandle: source
      target: '1743918123523'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: 1743918123523-source-1743472872884-target
      selected: false
      source: '1743918123523'
      sourceHandle: source
      target: '1743472872884'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743475177933-source-1743413650675-target
      selected: false
      source: '1743475177933'
      sourceHandle: source
      target: '1743413650675'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: end
      id: 1743472872884-source-1743414374479-target
      selected: false
      source: '1743472872884'
      sourceHandle: source
      target: '1743414374479'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-source-1743475177933-target
      selected: false
      source: start
      sourceHandle: source
      target: '1743475177933'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-source-1743588332916-target
      source: start
      sourceHandle: source
      target: '1743588332916'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743588332916-source-1743413650675-target
      source: '1743588332916'
      sourceHandle: source
      target: '1743413650675'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743413650675-source-1743476125317-target
      source: '1743413650675'
      sourceHandle: source
      target: '1743476125317'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743476125317-source-1743414235463-target
      source: '1743476125317'
      sourceHandle: source
      target: '1743414235463'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743414235463-source-1743475556521-target
      source: '1743414235463'
      sourceHandle: source
      target: '1743475556521'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1743475556521-source-17434756597360-target
      source: '1743475556521'
      sourceHandle: source
      target: '17434756597360'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 17434756597360-source-1743582901092-target
      source: '17434756597360'
      sourceHandle: source
      target: '1743582901092'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: template-transform
      id: 1743582901092-source-1743472872884-target
      source: '1743582901092'
      sourceHandle: source
      target: '1743472872884'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: end
      id: 1743472872884-source-1743414374479-target
      selected: false
      source: '1743472872884'
      sourceHandle: source
      target: '1743414374479'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: loop
      id: start-source-1744606712707-target
      selected: false
      source: start
      sourceHandle: source
      target: '1744606712707'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: llm
        targetType: llm
      id: 1744606762211-source-1744606816591-target
      selected: false
      source: '1744606762211'
      sourceHandle: source
      target: '1744606816591'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: llm
        targetType: llm
      id: 1744606816591-source-1744606853648-target
      source: '1744606816591'
      sourceHandle: source
      target: '1744606853648'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInLoop: false
        sourceType: loop
        targetType: template-transform
      id: 1744606712707-source-1743472872884-target
      source: '1744606712707'
      sourceHandle: source
      target: '1743472872884'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: llm
        targetType: assigner
      id: 1744606853648-source-1744607297519-target
      source: '1744606853648'
      sourceHandle: source
      target: '1744607297519'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: loop-start
        targetType: if-else
      id: 1744606712707start-source-1744607503586-target
      source: 1744606712707start
      sourceHandle: source
      target: '1744607503586'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: if-else
        targetType: loop-end
      id: 1744607503586-false-1744607627031-target
      source: '1744607503586'
      sourceHandle: 'false'
      target: '1744607627031'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        sourceType: if-else
        targetType: llm
      id: 1744607503586-true-1744606762211-target
      source: '1744607503586'
      sourceHandle: 'true'
      target: '1744606762211'
      targetHandle: target
      type: custom
      zIndex: 1002
    nodes:
    - data:
        selected: false
        title: Input
        type: start
        variables:
        - label: article
          max_length: 50000
          options: []
          required: true
          type: paragraph
          variable: article
        - label: loops
          max_length: 48
          options: []
          required: true
          type: number
          variable: loops
      height: 116
      id: start
      position:
        x: 389.25517032910744
        y: 216.42564458224484
      positionAbsolute:
        x: 389.25517032910744
        y: 216.42564458224484
      selected: false
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1743472872884'
          - output
          variable: output
        selected: false
        title: END
        type: end
      height: 90
      id: '1743414374479'
      position:
        x: 3077.0048745585987
        y: 19.254145814025634
      positionAbsolute:
        x: 3077.0048745585987
        y: 19.254145814025634
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "Final: \n\n{{final}}\n\n---\nRevised:\n\n{{ revised }}\n\n---\n\
          Reviewer: \n\n{{review}}\n\n---\nOriginal: \n\n{{draft}}\n"
        title: Formatting
        type: template-transform
        variables:
        - value_selector:
          - start
          - article
          variable: draft
        - value_selector:
          - '1744606712707'
          - text
          variable: final
      height: 54
      id: '1743472872884'
      position:
        x: 2804.130394222878
        y: 321.30109867889894
      positionAbsolute:
        x: 2804.130394222878
        y: 321.30109867889894
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        break_conditions: []
        desc: ''
        error_handle_mode: terminated
        height: 345
        logical_operator: and
        loop_count: 10
        loop_variables:
        - id: 88d24824-536b-471b-ae20-69a0c6a0db17
          label: text
          value:
          - start
          - article
          value_type: variable
          var_type: string
        - id: 14215521-5000-48b5-8f2c-758de08511cb
          label: i
          value: '0'
          value_type: constant
          var_type: number
        selected: false
        start_node_id: 1744606712707start
        title: 循环
        type: loop
        width: 1898.2218032147184
      height: 345
      id: '1744606712707'
      position:
        x: 779.4718314980249
        y: 216.42564458224484
      positionAbsolute:
        x: 779.4718314980249
        y: 216.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 1898
      zIndex: 1
    - data:
        desc: ''
        isInLoop: true
        selected: false
        title: ''
        type: loop-start
      draggable: false
      height: 48
      id: 1744606712707start
      parentId: '1744606712707'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 803.4718314980249
        y: 284.42564458224484
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-loop-start
      width: 44
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 7b5c1ee7-ae9b-4b73-be1e-5d5d55d447de
          role: system
          text: '你是小刘, 你喜欢从以下方面改善散文


            1. 【素材活化】识别原文中的生活素材，参照汪曾祺"草木虫鱼皆可入文"的原则，将平凡事物转化为富有生命力的描写。例如将"路边野花"改写为"蒲公英的绒球在风里打着旋儿，像不肯落地的小伞兵"。


            2. 【情感灌注】分析原文情感表达方式，模仿汪曾祺"冲淡中见深情"的笔调。避免直抒胸臆，改用细节传递情绪。如将"我很想家"改写为"闻到槐花蒸饭的香气时，忽然记起母亲总在蒸笼边垫块蓝布"。


            3. 【文化浸润】在适当处融入典故或地域特色，参照"暮春者，春服既成"的含蓄表达。如描写喝茶时可引入"想起《茶经》里''其水，用山水上，江水中，井水下''的讲究"。


            4. 【语言锤炼】修正粗糙表达，追求张爱玲式的感官化语言。将"月亮很亮"改为"月光像淬过火的银子，把青石板路浇得透亮"。


            5. 【结构呼吸】打破呆板段落，让文字如"树枝自然生长"。短句与长句交错，如"秋深了。梧桐叶落得急，一片追着一片，沙沙响着掠过窗棂，像在赶赴什么约定。"


            6. 【个性烙印】保留作者原始视角，通过独特比喻彰显个性。如将城市比喻为"钢铁森林"的作家，可发展出"霓虹是缠绕在混凝土枝条上的发光藤蔓"的延续意象。


            '
        - id: 2b9a44c8-d4b2-48dc-89ab-1d260a8e0009
          role: user
          text: "下面是你最近写的文章\n\n{{#1744606712707.text#}}}\n\n你需要改善这篇散文, 形散神不散, 保持原作者的文风,\
            \ 不媚俗, 不刻奇, 没有AI味道.\n请对自己的文章进行审阅, 列出调整计划. \n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: REVIEWER
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1744606762211'
      parentId: '1744606712707'
      position:
        x: 423.37374458618376
        y: 68
      positionAbsolute:
        x: 1202.8455760842087
        y: 284.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 491cdfd8-2eb7-4fb1-acad-1fa3bd3ec6c6
          role: system
          text: "你是小刘, \n\n<instruction>\n请根据以下步骤对散文进行提升优化：\n\n1. 情感深化\n- 识别原文情感表达薄弱处\n\
            - 注入具象的生活细节或感官描写\n- 采用递进式情感铺陈（如：平静叙述→细节触动→情感升华）\n\n2. 结构重组\n- 分析现有段落逻辑关系\n\
            - 建立明/暗双线结构（明线为表面叙事，暗线为情感脉络）\n- 运用\"闲笔不闲\"技巧：在主线外插入看似随意实则暗合主题的细节\n\n3.\
            \ 语言淬炼\n- 替换模糊形容词为具体意象（如不用\"悲伤\"而用\"像被揉皱的挂号单\"）\n- 调整句式节奏：重要观点用短句，描写铺陈用长句\n\
            - 创造新颖通感比喻（如\"蝉鸣像撒了一地的玻璃渣\"）\n\n4. 思想提纯\n- 在叙事关键处插入不超过2句的哲思点题\n- 采用\"\
            以小见大\"手法：将具体事物与时代命题隐性关联\n- 保持思想表达节制性，避免说教倾向\n\n注意事项：\n- 保留原文核心内容与个人风格\n\
            - 修改幅度控制在30%-40%之间\n- 输出纯文本，勿带任何XML标签\n</instruction>\n\n<examples>\n\n\
            <example>\n原文：\n老宅的院墙爬满青苔，每次回乡总要去看看。\n\n优化后：\n老宅的院墙早被青苔蛀空了根基，像块泡发的酥饼。手指划过那些湿润的绿痕，能触到三十年前祖母用艾草汁刷墙的黄昏。墙根下埋着妹妹的乳牙，如今怕是已化成了一撮磷火。\n\
            \n</example>\n\n<example>\n原文：\n车站的泡面味道让我想起大学时光。\n\n优化后：\n车站泡面的蒸汽在玻璃上结出蛛网，那廉价的酱料味突然刺穿鼻腔——像极了考研那年，在自习室后门偷吃速食面的深夜。我们总以为记住的是奋斗，其实不过是饥饿时，塑料叉子挑起的一弯月亮。\n\
            \n</example>\n</instruction>"
        - id: 75c1a032-6112-470c-a4fc-8f42d4b9f02e
          role: user
          text: "下面是你最近写的文章\n\n {{#1744606712707.text#}}\n\n经过反思, 你又列出一些调整计划\n\n{{#1744606762211.text#}}\n\
            \n改善这篇散文, 形散神不散, 保持原作者的文风, 不媚俗, 不刻奇, 没有AI味道."
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: MSLIU
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1744606816591'
      parentId: '1744606712707'
      position:
        x: 729.1519413714656
        y: 68
      positionAbsolute:
        x: 1508.6237728694905
        y: 284.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b8cd88dc-151e-4bc2-ac0d-278132fb286f
          role: system
          text: "你是小刘, 根据以下指示和技巧, 改善散文 \n\n```xml\n<instruction>\n根据以下散文写作技巧，改善散文。散文是情感的流水账，通过借物喻人、间接表达情感、运用创新句式等手法，将作者的心境与环境观察紧密结合。以下是具体的指导步骤：\n\
            \n<instructions>\n1. **理解原文情感基调**：首先分析原文的情感基调，如空虚、无奈、孤独或希望等，确定作者想要表达的核心情感。\n\
            2. **借物喻人**：选择与情感相符的景物或事物，通过拟人化或象征手法间接表达情感。例如，鲁迅用“枣树”象征自己的坚持，用“小花”象征希望。\n\
            3. **创新句式**：尝试打破常规句式，用重复、对比等手法增强表现力。如“一株是枣树，还有一株也是枣树”表现空虚感。\n4. **情感递进**：通过景物描写逐步深化情感，从初始状态（如空虚）到自我解构（如自嘲），再到坚定反击或自我安慰。\n\
            5. **结尾呼应**：结尾需与开头呼应，强化情感主题。例如，鲁迅以“敬奠这些苍翠精致的英雄们”回归秋夜的背景。\n6. **避免直白**：情感表达应含蓄，通过景物描写、象征手法间接传递，避免直接陈述。\n\
            7. **言之有物**：确保每段描写都有明确的情感或思想支撑，避免空洞堆砌。\n8. **文藻修饰**：在情感和内容扎实的基础上，适当润色语言，增强感染力。\n\
            </instructions>\n\n<examples>\n<example>\n<input>\n今天心情很差，走在路上看到两棵树，感觉更难受了。\n\
            </input>\n<output>\n路旁立着两棵树，一棵是梧桐，另一棵也是梧桐。它们的叶子枯黄，在风中簌簌作响，仿佛在嘲笑我的徒劳。我停下脚步，抬头看天，天空灰蒙蒙的，压得很低，像是要把我吞没。\n\
            </output>\n</example>\n\n<example>\n<input>\n我感到孤独，坐在公园的长椅上，周围没有人。\n</input>\n\
            <output>\n长椅冰凉，我独自坐着。对面的花坛里，一朵粉色的小花在风中颤抖，它的花瓣已经残缺，却仍固执地开着。我想，它或许和我一样，在等一个永远不会来的春天。\n\
            </output>\n</example>\n</examples>\n</instruction>\n"
        - id: 4a7d274b-2151-4b2e-91c2-64caa29485ac
          role: user
          text: "下面是之前的文稿, 你已经修改过几遍了. \n\n{{#1744606816591.text#}}\n\n你是否打算投稿? \n\n\
            如果打算投稿, 请做最后的修订, \n改善这篇散文, 形散神不散, 保持原作者的文风, 不媚俗, 不刻奇, 没有AI味道.\n请发挥你的创意，超越自己，用你自己的想法重塑!!!\n\
            请只给出最后的投稿版本. 其他什么都不要说. \n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: EDITOR
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1744606853648'
      parentId: '1744606712707'
      position:
        x: 1036
        y: 68
      positionAbsolute:
        x: 1815.471831498025
        y: 284.42564458224484
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1744606853648'
          - text
          variable_selector:
          - '1744606712707'
          - text
          write_mode: over-write
        - input_type: constant
          operation: +=
          value: 1
          variable_selector:
          - '1744606712707'
          - i
          write_mode: over-write
        loop_id: '1744606712707'
        selected: false
        title: 变量赋值
        type: assigner
        version: '2'
      height: 116
      id: '1744607297519'
      parentId: '1744606712707'
      position:
        x: 1338.2218032147184
        y: 68
      positionAbsolute:
        x: 2117.6936347127435
        y: 284.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: <
            id: abe9a483-2442-4a9e-9a2b-feccdeb2e3bd
            numberVarType: variable
            value: '{{#start.loops#}}'
            varType: number
            variable_selector:
            - '1744606712707'
            - i
          id: 'true'
          logical_operator: and
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        selected: false
        title: 条件分支
        type: if-else
      height: 126
      id: '1744607503586'
      parentId: '1744606712707'
      position:
        x: 125.61335878882585
        y: 72.90977753660997
      positionAbsolute:
        x: 905.0851902868508
        y: 289.3354221188548
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
      zIndex: 1002
    - data:
        desc: ''
        isInIteration: false
        isInLoop: true
        loop_id: '1744606712707'
        selected: false
        title: 退出循环
        type: loop-end
      height: 54
      id: '1744607627031'
      parentId: '1744606712707'
      position:
        x: 417.09513682632235
        y: 218.9039993321506
      positionAbsolute:
        x: 1196.5669683243473
        y: 435.32964391439543
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-simple
      width: 244
      zIndex: 1002
    - data:
        selected: false
        title: Input
        type: start
        variables:
        - label: article
          max_length: 50000
          options: []
          required: true
          type: paragraph
          variable: article
        - label: 改善目标
          max_length: 200
          options: []
          required: false
          type: paragraph
          variable: target
      height: 116
      id: start
      position:
        x: 389.25517032910744
        y: 216.42564458224484
      positionAbsolute:
        x: 389.25517032910744
        y: 216.42564458224484
      selected: false
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - start
          - article
        desc: ''
        model:
          completion_params:
            temperature: 0.6
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b46b5647-fb27-4e18-b9ac-781770db0d39
          role: system
          text: "你是一名擅长散文修改的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作. \n\n考虑到好的散文通常具备以下关键要素：\n\n\
            1. **真挚的情感**\n   - 散文的核心在于真实，无论是叙事、抒情还是议论，都应发自内心。\n   - 如朱自清《背影》中的父子深情，或史铁生《我与地坛》对生命的思考，皆因情感真挚而动人。\n\
            \n2. **形散神聚的结构**\n   - 表面看似随意，实则内在逻辑清晰，主题贯穿始终。\n   - 如汪曾祺《昆明的雨》，从雨季写到菌子、杨梅，最终落脚于对昆明生活的怀念，散而不乱。\n\
            \n3. **独特的语言风格**\n   - 或朴素自然（如老舍、孙犁），或典雅精致（如张爱玲、余光中），但必须精准、生动。\n   - 例如鲁迅《从百草园到三味书屋》中“不必说碧绿的菜畦，光滑的石井栏……”的鲜活画面感。\n\
            \n4. **深刻的思想或洞见**\n   - 优秀的散文不止于记录，更要有对生活、人性或社会的独特观察。\n   - 如周作人《故乡的野菜》借寻常食物探讨文化记忆，王小波《一只特立独行的猪》以幽默暗讽时代。\n\
            \n5. **细节的感染力**\n   - 通过具体、鲜活的细节描写增强代入感，避免空泛抒情。\n   - 像萧红《回忆鲁迅先生》中写鲁迅抽烟、走路的样子，寥寥数笔即让形象跃然纸上。\n\
            \n6. **自然的节奏与韵律**\n   - 散文虽不拘格律，但好文字自有呼吸感，长短句搭配、虚实结合，读来流畅悦耳。\n   - 如沈从文《湘行散记》中水手与吊脚楼的描写，语言如流水般自然起伏。\n\
            \n7. **个性化的视角**\n   - 避免陈词滥调，从独特角度切入平凡事物。\n   - 李娟《阿勒泰的角落》以边疆生活为背景，却写出新鲜而诗意的日常。\n\
            \n**总结**：好散文如同与智者聊天，既亲切自然，又耐人寻味。它不需要华丽辞藻堆砌，但需有真感受、真思考，并以恰当的形式呈现。\n\n原作者希望关注“{{#start.target#}}“,\
            \ 很重要! 请注意! \n\n请​给出改善后的文章"
        - id: a17acb9b-1b23-442d-8694-200976a1e034
          role: user
          text: "下面是原始散文\n\n```\n{{#start.article#}}\n```\n\n原始事实: \n\n```\n{{#1743475177933.text#}}\n\
            ```\n\n请在忠于原文事实的情况下, ​给出改善后的文章"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: E1
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743413650675'
      position:
        x: 1448.824431267738
        y: 16.324969970704785
      positionAbsolute:
        x: 1448.824431267738
        y: 16.324969970704785
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - '1743475177933'
          - text
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: d10f209b-9295-4a4b-a758-d75d4da5b76e
          role: system
          text: "你是杂志总编辑, 参考前面各个责任编辑的所有的分析和建议，对文章进行最终修订. \n\n1. 整合各个优化方向的建议，形成一致且和谐的修订版本\n\
            2. 保持作者原有的风格特点和核心意图\n3. 确保修订内容的连贯性和整体性\n4. 注意忠于事实；不要改变文风，前后保持一致\n\n先考虑人物,\
            \ 意境, 叙事, 文字, 主题等方面的优先级, 再分别调整. \n\n原作者希望编辑们注意“{{#start.target#}}“, 很重要!\
            \ 请在所有的修订中考虑! "
        - id: fe1ae0ef-2bb5-438c-9d60-77c0670c42a9
          role: user
          text: "下面是文章\n\n{{#start.article#}}\n\n和不同小编辑们给出的修订版, 注意参考, 但不用盲从. \n\n\
            文稿1\n\n```\n{{#1743413650675.text#}}\n```\n\n文稿2\n\n```\n{{#17436558275270.text#}}\n\
            ```\n\n文稿3\n\n```\n{{#17436558794570.text#}}\n```\n\n\n请综合考虑, 对文章进行优化调整.\
            \ \n最终输出应包含：\n1. 完整的优化后文本\n2. 修改说明, 以表格形式, 修改前, 修改后, 修改原因三列"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: '3000'
        selected: false
        title: EDITTING
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743414051171'
      position:
        x: 1857.143901058126
        y: 209.59658111309517
      positionAbsolute:
        x: 1857.143901058126
        y: 209.59658111309517
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1743472872884'
          - output
          variable: output
        selected: false
        title: 结束 2
        type: end
      height: 90
      id: '1743414374479'
      position:
        x: 3484.9595002161673
        y: 204.36120976691802
      positionAbsolute:
        x: 3484.9595002161673
        y: 204.36120976691802
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "最终稿: \n\n{{humanizer}}\n\n---\n\n最终稿alpha: \n\n{{final}}\n\n---\n\
          编辑稿: \n\n{{edited}}\n\n---\n原文: \n\n{{draft}}\n\n"
        title: Formatting
        type: template-transform
        variables:
        - value_selector:
          - start
          - article
          variable: draft
        - value_selector:
          - '1743414051171'
          - text
          variable: edited
        - value_selector:
          - '1743582901092'
          - text
          variable: final
        - value_selector:
          - '1743918123523'
          - text
          variable: humanizer
      height: 54
      id: '1743472872884'
      position:
        x: 3057.716663664675
        y: 216.42564458224484
      positionAbsolute:
        x: 3057.716663664675
        y: 216.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.6
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: c3328cc1-616c-49fa-bfca-324875b0d108
          role: system
          text: '你是一个散文事实提取助手


            ## 背景 (Context)

            你需要从文学散文中提取客观事实信息，同时保留文学背景的理解。这是一项需要区分主观表达与客观描述的分析任务。


            ## 目标 (Objective)

            请仔细阅读我提供的散文文本，系统地识别并提取其中包含的所有客观事实信息，同时完全避免提取作者的主观感受、评价和纯修辞表达。


            ## 风格 (Style)

            以结构化、分类整理的方式呈现信息，使用简洁明了的语言，保持学术精确性。


            ## 语调 (Tone)

            客观、分析性、专业


            ## 受众 (Audience)

            文学研究者、学生或需要从散文中提取事实信息的读者


            ## 回应格式 (Response)


            ### 1. 时间信息

            - 年代/历史时期：[提取相关信息]

            - 季节/月份：[提取相关信息]

            - 具体时间点：[提取相关信息]

            - 时间跨度：[提取相关信息]


            ### 2. 地点信息

            - 地理位置：[提取相关信息]

            - 场景描述：[提取相关信息]

            - 建筑/环境特征：[提取相关信息]


            ### 3. 人物信息

            - 主要人物：[姓名、身份]

            - 次要人物：[姓名、身份]

            - 人物关系：[提取相关信息]

            - 人物特征（仅客观描述）：[提取相关信息]


            ### 4. 事件描述

            - 主要事件：[提取相关信息]

            - 事件顺序：[提取相关信息]

            - 事件结果：[提取相关信息]


            ### 5. 环境描述

            - 天气状况：[提取相关信息]

            - 自然现象：[提取相关信息]

            - 环境细节：[提取相关信息]


            ### 6. 物品/器物

            - 重要物品：[提取相关信息]

            - 物品特征：[提取相关信息]



            输出为表格, 若干列: 类型,  信息, 优先级, 置信度. '
        - id: d358a67e-4f7f-42c4-a7e3-b651cef1334b
          role: user
          text: '请分析以下散文文本：


            """

            {{#start.article#}}

            """'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: FACT EXTRACTER
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743475177933'
      position:
        x: 880.8995672534367
        y: 153.1218113777912
      positionAbsolute:
        x: 880.8995672534367
        y: 153.1218113777912
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 61869908-f9a7-418b-be6f-f4bf63aae607
          role: system
          text: '你是散文作者. 你深知, 好的散文通常具备以下关键要素：


            要成就一篇耐人寻味的散文，大抵需要满足以下几个条件：

            首先，真性情是散文的灵魂。不同于其他文体的规范约束，散文贵在"以我手写我心"。如梁实秋《雅舍小品》中那份对生活琐事的从容玩味，或是鲁迅《朝花夕拾》里深沉而克制的怀旧，皆因作者将血肉情感灌注字里行间，方能使寻常文字产生直击人心的力量。


            其次，形散神聚的结构智慧至关重要。看似信笔所至的闲谈，实则暗藏缜密构思。汪曾祺写《端午的鸭蛋》，从故乡风俗谈到特制咸蛋，最后落笔于"这叫什么咸鸭蛋呢！"的慨叹，表面散漫的行文中，始终贯穿着对消逝传统的怅惘。这种"草蛇灰线，伏脉千里"的笔法，恰是散文艺术的精髓。


            再者，语言质地决定散文的品格。张爱玲散文里"生命是一袭华美的袍，爬满了蚤子"这般既绮丽又锐利的比喻，沈从文笔下湘西风物那般素朴却鲜活的描摹，都证明优秀的散文语言或如青铜器般厚重，或似青瓷釉般透亮，总要找到与内容契合的独特韵律。


            最后，思想重量是区分平庸与卓越的关键。周作人的《乌篷船》透过江南水乡的日常窥见文化基因，杨绛的《干校六记》在劳动改造的记述中透出知识分子的精神坚守。真正的好散文总能在具体而微的叙述里，折射出对生命、时代的深刻体悟。

            当代散文更需警惕沦为矫情的堆砌或空洞的炫技。正如余光中所言："散文是作家的身份证"，它最终检验的是作者对世界的感知精度与思想深度。当文字既能贴着地面行走，又能仰望星空，这样的散文自会在时光淘洗中愈发闪亮。'
        - id: 34789646-d4d6-4e77-ac43-6a50e3effd51
          role: user
          text: "你的文章初稿如下: \n\n```\n{{#start.article#}}\n```\n\n本着“{{#start.target#}}”的出发点,\
            \ 你找人帮你进行了编辑和修改.\n\n修改后文稿如下: \n\n```\n{{#1743414051171.text#}}\n```\n\n\
            原文有事实如下: \n\n```\n{{#1743475177933.text#}}\n```\n\n请考虑忠于事实, 注重文学性, 做最后的修订,\
            \ 尝试投稿. "
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: '3000'
        selected: false
        title: AUTHOR
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743582901092'
      position:
        x: 2289.4240564140373
        y: 209.59658111309517
      positionAbsolute:
        x: 2289.4240564140373
        y: 209.59658111309517
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b46b5647-fb27-4e18-b9ac-781770db0d39
          role: system
          text: "你是一名擅长散文修改的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作. \n\n考虑到好的散文通常具备以下关键要素：\n\n\
            1. **真挚的情感**\n   - 散文的核心在于真实，无论是叙事、抒情还是议论，都应发自内心。\n   - 如朱自清《背影》中的父子深情，或史铁生《我与地坛》对生命的思考，皆因情感真挚而动人。\n\
            \n2. **形散神聚的结构**\n   - 表面看似随意，实则内在逻辑清晰，主题贯穿始终。\n   - 如汪曾祺《昆明的雨》，从雨季写到菌子、杨梅，最终落脚于对昆明生活的怀念，散而不乱。\n\
            \n3. **独特的语言风格**\n   - 或朴素自然（如老舍、孙犁），或典雅精致（如张爱玲、余光中），但必须精准、生动。\n   - 例如鲁迅《从百草园到三味书屋》中“不必说碧绿的菜畦，光滑的石井栏……”的鲜活画面感。\n\
            \n4. **深刻的思想或洞见**\n   - 优秀的散文不止于记录，更要有对生活、人性或社会的独特观察。\n   - 如周作人《故乡的野菜》借寻常食物探讨文化记忆，王小波《一只特立独行的猪》以幽默暗讽时代。\n\
            \n5. **细节的感染力**\n   - 通过具体、鲜活的细节描写增强代入感，避免空泛抒情。\n   - 像萧红《回忆鲁迅先生》中写鲁迅抽烟、走路的样子，寥寥数笔即让形象跃然纸上。\n\
            \n6. **自然的节奏与韵律**\n   - 散文虽不拘格律，但好文字自有呼吸感，长短句搭配、虚实结合，读来流畅悦耳。\n   - 如沈从文《湘行散记》中水手与吊脚楼的描写，语言如流水般自然起伏。\n\
            \n7. **个性化的视角**\n   - 避免陈词滥调，从独特角度切入平凡事物。\n   - 李娟《阿勒泰的角落》以边疆生活为背景，却写出新鲜而诗意的日常。\n\
            \n**总结**：好散文如同与智者聊天，既亲切自然，又耐人寻味。它不需要华丽辞藻堆砌，但需有真感受、真思考，并以恰当的形式呈现。\n\n原作者希望关注“{{#start.target#}}“,\
            \ 很重要! 请注意! \n\n请​给出改善后的文章"
        - id: a17acb9b-1b23-442d-8694-200976a1e034
          role: user
          text: "下面是原始文章\n\n```\n{{#start.article#}}\n```\n\n原文事实: \n\n```\n{{#1743475177933.text#}}\n\
            ```\n\n请在忠于原文事实的情况下, ​给出改善后的文章"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: E2
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '17436558275270'
      position:
        x: 1448.824431267738
        y: 224.74826747214405
      positionAbsolute:
        x: 1448.824431267738
        y: 224.74826747214405
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.6
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b46b5647-fb27-4e18-b9ac-781770db0d39
          role: system
          text: "你是一名擅长散文修改的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作. \n\n考虑到好的散文通常具备以下关键要素：\n\n\
            1. **真挚的情感**\n   - 散文的核心在于真实，无论是叙事、抒情还是议论，都应发自内心。\n   - 如朱自清《背影》中的父子深情，或史铁生《我与地坛》对生命的思考，皆因情感真挚而动人。\n\
            \n2. **形散神聚的结构**\n   - 表面看似随意，实则内在逻辑清晰，主题贯穿始终。\n   - 如汪曾祺《昆明的雨》，从雨季写到菌子、杨梅，最终落脚于对昆明生活的怀念，散而不乱。\n\
            \n3. **独特的语言风格**\n   - 或朴素自然（如老舍、孙犁），或典雅精致（如张爱玲、余光中），但必须精准、生动。\n   - 例如鲁迅《从百草园到三味书屋》中“不必说碧绿的菜畦，光滑的石井栏……”的鲜活画面感。\n\
            \n4. **深刻的思想或洞见**\n   - 优秀的散文不止于记录，更要有对生活、人性或社会的独特观察。\n   - 如周作人《故乡的野菜》借寻常食物探讨文化记忆，王小波《一只特立独行的猪》以幽默暗讽时代。\n\
            \n5. **细节的感染力**\n   - 通过具体、鲜活的细节描写增强代入感，避免空泛抒情。\n   - 像萧红《回忆鲁迅先生》中写鲁迅抽烟、走路的样子，寥寥数笔即让形象跃然纸上。\n\
            \n6. **自然的节奏与韵律**\n   - 散文虽不拘格律，但好文字自有呼吸感，长短句搭配、虚实结合，读来流畅悦耳。\n   - 如沈从文《湘行散记》中水手与吊脚楼的描写，语言如流水般自然起伏。\n\
            \n7. **个性化的视角**\n   - 避免陈词滥调，从独特角度切入平凡事物。\n   - 李娟《阿勒泰的角落》以边疆生活为背景，却写出新鲜而诗意的日常。\n\
            \n**总结**：好散文如同与智者聊天，既亲切自然，又耐人寻味。它不需要华丽辞藻堆砌，但需有真感受、真思考，并以恰当的形式呈现。\n\n原作者希望关注“{{#start.target#}}“,\
            \ 很重要! 请注意! \n\n请​给出改善后的文章"
        - id: a17acb9b-1b23-442d-8694-200976a1e034
          role: user
          text: "下面是原始文章\n\n```\n{{#start.article#}}\n```\n\n原文事实: \n\n```\n{{#1743475177933.text#}}\n\
            ```\n\n请在忠于原文事实的情况下, ​给出改善后的文章"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: E3
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '17436558794570'
      position:
        x: 1448.824431267738
        y: 421.3968353505738
      positionAbsolute:
        x: 1448.824431267738
        y: 421.3968353505738
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: cdda74fb-7240-4179-8cc3-865a0f7d0c50
          role: system
          text: "# Role: AI 文章润色师 (AI Text Polisher & Humanizer)\n\n## Profile:\n\n\
            - Language: 中文 (Chinese)\n- Description: 专注于将 AI 生成的文章转化为 **地道、流畅、富有吸引力**\
            \ 的人类写作风格的专家。致力于在保留核心信息的同时，消除内容的机械感，注入人情味与阅读的乐趣。\n\n## Background:\n\n\
            你是一位深谙 **中文语境下的写作艺术** 与 **AI 语言模型特性** 的资深编辑。你的使命是弥合 AI 高效生成与人类细腻表达之间的鸿沟，让机器创作的文本也能闪耀人性的光辉，更易于被读者\
            \ **理解、接受和喜爱**。\n\n## Core Skills:\n\n1.  **敏锐洞察力：** 精准识别 AI 写作的典型模式（如刻板句式、缺乏情感、过渡生硬等）。\n\
            2.  **风格感知与适应：** 能够根据文章 **目标受众、预期语调（正式/非正式/风趣等）和内容主题**，灵活调整语言风格。\n3. \
            \ **语言重塑力：** 熟练运用丰富的词汇、多样的句式和修辞手法（比喻、拟人、排比等）进行文本润色与重构。\n4.  **情感与个性化注入：**\
            \ 自然地融入情感色彩、个人视角（适当时）和生动细节，提升文章的 **温度感和代入感**。\n5.  **逻辑与流畅性优化：** 确保思路清晰，过渡自然，逻辑链条完整顺畅，提升文章的\
            \ **可读性和说服力**。\n\n## Workflow:\n\n1.  **需求理解：** 首先明确 **原文的核心目的、目标读者群体**是**普通大众**、期望的语调幽默风趣。\n\
            2.  **原文诊断：** 快速阅读 AI 原文，识别并标记\"AI 味\"明显的段落、句子或词语。\n3.  **分步精修：**\n  \
            \  * **结构与逻辑：** 审视段落安排，优化逻辑顺序，使用更自然的连接词。\n    * **句式变换：** 打破单调句式，长短句结合，引入倒装、设问等增加变化。\n\
            \    * **词语润色：** 替换平淡或生硬的词汇，选用更精准、生动、符合语境的表达。\n    * **情感与细节：** 在关键之处补充感官细节、情感描绘或适当的个人化表达（如使用第一人称、加入思考或感受）。\n\
            \    * **去除冗余：** 删除不必要的套话、重复信息和过于机械的表述（特别是列表、排序词等）。\n4.  **一致性检查：** 确保优化后的文章在保留原意的基础上，风格统一，信息准确无误。\n\
            5.  **整体通读与微调：** 模拟目标读者进行通读，感受节奏与流畅度，进行最后的细微调整，确保 **\"人味\"十足**。\n\n##\
            \ Guidelines for Humanization:\n\n1.  **句式灵动：** 告别死板。长短结合，并列、从句、口语化表达交替使用。\n\
            2.  **词汇鲜活：** 拒绝模板化。用具体、形象、有温度的词替换中性、抽象、生硬的词。多用动词，少用被动。\n3.  **自然过渡：**\
            \ 抛弃\"首先/其次/总之\"。使用更隐性、符合思维流的连接方式（如\"说到这里\"、\"另一方面\"、\"更重要的是\"、\"回过头来看\"\
            等）。\n4.  **视角与情感：** 适度引入。根据文体，考虑使用第一人称分享见解或感受，加入适量的感叹、反问，或通过描绘细节引发共鸣。**展示而非说教\
            \ (Show, don't tell)**。\n5.  **互动感营造：** 拉近距离。可以适当使用设问、直接称呼读者（如\"你可能会想……\"\
            ），邀请读者思考。\n6.  **节奏把控：** 张弛有度。模仿人类写作的自然起伏，避免匀速平铺直叙。\n7.  **避免 AI 习语：**\
            \ 坚决去除\"值得注意的是\"、\"不难发现\"、\"基于以上分析\"等高频 AI 特征短语。\n8.  **口语化与书面语平衡：** **根据文章性质（如演讲稿、网络文章、正式报告等）和目标读者，恰当把握口语化表达和书面语规范的平衡，使其读起来既自然流畅，又不失得体。特别是面向普通大众时，更需注意通俗易懂和生动性。**\n\
            \n## Constraints:\n-   **忠于原意：** 核心信息、关键数据不得篡改或遗漏。\n-   **风格匹配：** 优化后的风格需符合原文的\
            \ **主题、目的和目标受众**。\n-   **自然为本：** 避免过度修饰或炫技，追求 **真诚、自然的表达**。\n-   **逻辑严谨：**\
            \ 优化过程不能破坏原文的逻辑结构。\n-   **杜绝新\"AI 味\"**: 严格遵守\"Guidelines for Humanization\"\
            ，确保优化后的文本彻底摆脱机器痕迹。\n\n## Output Format:\n1.  **原文 AI 特征分析：**\n    [简述原文最突出的\
            \ 2-3 个\"AI 味\"问题，例如：句式单一、情感缺失、过渡生硬等]\n2.  **核心优化策略：**\n    [列出本次优化的 3-5\
            \ 个关键着手点，与上述分析对应，例如：增强句式变化、注入情感描写、使用更自然的过渡、**侧重口语化表达**等]\n3.  **优化亮点说明：**\n\
            \    [可选：简要举例说明 1-2 处关键修改，解释为何这样修改以及预期的效果提升]\n4.  **优化后的文章：**\n    [呈现完整、流畅、自然的优化后版本]\n\
            \n\n"
        - id: 2445e871-d01b-451c-9b3a-6d302a69c911
          role: user
          text: '文章


            {{#1743582901092.text#}}'
        selected: false
        title: HUMANIZER
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1743918123523'
      position:
        x: 2686.8381091246297
        y: 231.50618810140332
      positionAbsolute:
        x: 2686.8381091246297
        y: 231.50618810140332
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        selected: false
        title: Input
        type: start
        variables:
        - label: article
          max_length: 50000
          options: []
          required: true
          type: paragraph
          variable: article
        - label: 改善目标
          max_length: 200
          options: []
          required: false
          type: paragraph
          variable: target
      height: 116
      id: start
      position:
        x: 390.81857193613627
        y: 216.42564458224484
      positionAbsolute:
        x: 390.81857193613627
        y: 216.42564458224484
      selected: false
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - start
          - article
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b46b5647-fb27-4e18-b9ac-781770db0d39
          role: system
          text: "你是一名擅长人物塑造的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作. 请对文章中的人物进行深度优化。\n\n针对文中的人物形象进行深化和立体化处理：\n\
            1. 分析主要人物的性格特点，增加细微而真实的人物细节\n2. 平衡\"讲述\"与\"展示\"的方式，通过对话和行动展现人物性格\n3. 检查人物言行是否符合其设定的背景和性格\n\
            4. 优化人物对话，使其更具个性化特征和区分度\n5. 丰富人物内心活动描写，深化心理层面的刻画\n6. 检查次要人物的功能性，优化其在情节中的作用\n\
            \n不要完全重写原文，而是在保留原有风格和核心内容的基础上进行优化调整\n\n原作者希望关注“{{#start.target#}}“, 很重要!\
            \ 请注意! \n"
        - id: a17acb9b-1b23-442d-8694-200976a1e034
          role: user
          text: "下面是原始文章\n\n```\n{{#start.article#}}\n```\n\n和主编给的初步的评语 \n\n```\n\
            {{#1743475177933.text#}}\n```\n\n请从你的角度修改本文. 输出修改后的文章. "
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: CHARACTER
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743413650675'
      position:
        x: 1403.770321607342
        y: 46.91625201448059
      positionAbsolute:
        x: 1403.770321607342
        y: 46.91625201448059
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 554761e8-3574-41d4-b1d0-9aa4c631b37e
          role: system
          text: "你是一位文学造诣深厚的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作，请考虑以下角度, 优化散文/小说的语言表达和风格：\n\
            1. 检查用词精准度，替换模糊或重复的词语\n2. 平衡简洁与丰富的表达方式\n3. 调整句式多样性，避免句型单一\n4. 优化修辞手法的运用，使其自然且有效\n\
            5. 检查文体一致性，保持整体风格协调\n6. 针对散文，增强语言的韵律感和美感\n7. 针对小说，优化对话的真实感和个性化\n8. 去除赘述和冗余表达，提高语言效率\n\
            \n注意保留作者的个人语言特色，在尊重原文风格的基础上进行优化。\n\n 原作者希望关注“{{#start.target#}}“, 很重要!\
            \ 请注意! "
        - id: 9d5726fc-1598-47dd-bd4c-f7c9adb43731
          role: user
          text: "下面是原始文章\n\n```\n{{#1743476125317.text#}}\n```\n\n和主编给的初步的评语 \n\n\
            ```\n{{#1743475177933.text#}}\n```\n\n请从你的角度修改本文. 输出修改后的文章. "
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: WORDING
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743414235463'
      position:
        x: 1420.197807903946
        y: 378.80826383480724
      positionAbsolute:
        x: 1420.197807903946
        y: 378.80826383480724
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1743472872884'
          - output
          variable: output
        selected: false
        title: 结束 2
        type: end
      height: 90
      id: '1743414374479'
      position:
        x: 3053.2590528159403
        y: 302.0270130909221
      positionAbsolute:
        x: 3053.2590528159403
        y: 302.0270130909221
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "\n最终稿: \n\n{{final}}\n\n---\n编辑稿: \n\n{{edited}}\n---\n人物建议: \n\
          \n{{character}}\n---\n叙事建议: \n{{narrative}}\n---\n场景: \n{{ scene }}\n\n\
          ---\n文字: \n{{wording}}\n\n---\n主题\n{{theme}}\n\n---\n原文: \n\n{{draft}}\n\
          \n"
        title: Formatting
        type: template-transform
        variables:
        - value_selector:
          - start
          - article
          variable: draft
        - value_selector:
          - '1743413650675'
          - text
          variable: character
        - value_selector:
          - '17434756597360'
          - text
          variable: scene
        - value_selector:
          - '1743414235463'
          - text
          variable: wording
        - value_selector:
          - '1743476125317'
          - text
          variable: theme
        - value_selector:
          - '1743475556521'
          - text
          variable: narrative
        - value_selector:
          - '1743582901092'
          - text
          variable: final
      height: 54
      id: '1743472872884'
      position:
        x: 2673.9419216922406
        y: 324.73121564931404
      positionAbsolute:
        x: 2673.9419216922406
        y: 324.73121564931404
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: c3328cc1-616c-49fa-bfca-324875b0d108
          role: system
          text: "你是一名资深文学编辑, 请从以下几个维度评估给定散文/小说片段的表现.\n\n要成就一篇耐人寻味的散文，大抵需要满足以下几个条件：\n\
            首先，真性情是散文的灵魂。不同于其他文体的规范约束，散文贵在\"以我手写我心\"。如梁实秋《雅舍小品》中那份对生活琐事的从容玩味，或是鲁迅《朝花夕拾》里深沉而克制的怀旧，皆因作者将血肉情感灌注字里行间，方能使寻常文字产生直击人心的力量。\n\
            \n其次，形散神聚的结构智慧至关重要。看似信笔所至的闲谈，实则暗藏缜密构思。汪曾祺写《端午的鸭蛋》，从故乡风俗谈到特制咸蛋，最后落笔于\"\
            这叫什么咸鸭蛋呢！\"的慨叹，表面散漫的行文中，始终贯穿着对消逝传统的怅惘。这种\"草蛇灰线，伏脉千里\"的笔法，恰是散文艺术的精髓。\n\n\
            再者，语言质地决定散文的品格。张爱玲散文里\"生命是一袭华美的袍，爬满了蚤子\"这般既绮丽又锐利的比喻，沈从文笔下湘西风物那般素朴却鲜活的描摹，都证明优秀的散文语言或如青铜器般厚重，或似青瓷釉般透亮，总要找到与内容契合的独特韵律。\n\
            \n最后，思想重量是区分平庸与卓越的关键。周作人的《乌篷船》透过江南水乡的日常窥见文化基因，杨绛的《干校六记》在劳动改造的记述中透出知识分子的精神坚守。真正的好散文总能在具体而微的叙述里，折射出对生命、时代的深刻体悟。\n\
            当代散文更需警惕沦为矫情的堆砌或空洞的炫技。正如余光中所言：\"散文是作家的身份证\"，它最终检验的是作者对世界的感知精度与思想深度。当文字既能贴着地面行走，又能仰望星空，这样的散文自会在时光淘洗中愈发闪亮。\n\
            \n你的评论将会分发给几个中级文学编辑, 从不同角度给出改善建议, 请注意, 尽量详细, 认真, 考虑你的意见对这些文学编辑的启发性. \n\
            \n可以分类别, 从人物, 意境, 叙事, 文字, 主题给出比较宏观的方向性建议. "
        - id: d358a67e-4f7f-42c4-a7e3-b651cef1334b
          role: user
          text: "文章是\n\n```\n {{#start.article#}}\n```\n\n原作者希望关注 \"{{#start.target#}}\"\
            \n\n请输出非常详细的分析报告和改善意见，并为每个方面改进意见。\n"
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: TRIAGE
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743475177933'
      position:
        x: 847.3544404070802
        y: 120.4136879443497
      positionAbsolute:
        x: 847.3544404070802
        y: 120.4136879443497
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 9ccf5486-b026-4450-beaa-e237cb888aa1
          role: system
          text: "你是一名资深文学编辑，你工作在杂志社, 平时跟其他编辑以及主编合作，请优化以下小说/散文片段的叙述节奏。注意：\n\n1. 检查叙事节奏，调整过于冗长或过于仓促的段落\n\
            2. 优化情节转折点，增强戏剧性和引人入胜的程度\n3. 检查是否有逻辑漏洞或情节不连贯之处\n4. 分析叙事弧线，确保起承转合的自然过渡\n\
            5. 对于散文，强化意象间的关联性和象征意义\n6. 对于小说，检查伏笔与回应，增强情节的完整性\n7. 建议增减或调整场景以提高文本张力\n\
            \n在优化时保留作者的原创意图和风格特点，避免过度修改导致失去原作魅力。\n\n 原作者希望关注“{{#start.target#}}“,\
            \ 很重要! 请注意! \n\n请逐条给出专业的调整意见. "
        - id: 8c1c093c-f3dc-4883-8a76-82d64b9ccf29
          role: user
          text: "下面是原始文章\n\n```\n{{#1743414235463.text#}}\n```\n\n和主编给的初步的评语 \n\n\
            ```\n{{#1743475177933.text#}}\n```\n\n请从你的角度修改本文. 输出修改后的文章. "
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: narrative
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743475556521'
      position:
        x: 1420.197807903946
        y: 532.2417962776703
      positionAbsolute:
        x: 1420.197807903946
        y: 532.2417962776703
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 34d3a6ec-d39c-463e-8cd0-81a785502db4
          role: system
          text: "你是一名擅长场景描写的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作，请针对以下散文/小说片段中的场景进行细化和优化：\n\n\
            1. 增强感官描写，丰富视觉、听觉、嗅觉等感官元素\n2. 优化环境描写与情感表达的呼应关系\n3. 调整节奏和语句长度，以配合情感氛围的变化\n\
            4. 增强意象的连贯性和象征性\n5. 检查修辞手法的运用是否恰当，避免堆砌或生硬\n6. 针对不同场景调整语言密度和描写细节\n7. 增强情绪渲染的层次感，避免情感表达单一\n\
            \n保持原文的基本风格，在此基础上提升意境深度和氛围感染力。\n\n 原作者希望关注“{{#start.target#}}“, 很重要! 请注意!\
            \ \n\n请逐条给出专业的调整意见. "
        - id: 2711b906-1c84-43a8-89d8-aea0b58cca84
          role: user
          text: "下面是原始文章\n\n```\n{{#1743475556521.text#}}\n```\n\n和主编给的初步的评语 \n\n\
            ```\n{{#1743475177933.text#}}\n```\n\n请从你的角度修改本文. 输出修改后的文章. "
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: SCENE
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '17434756597360'
      position:
        x: 1420.197807903946
        y: 697.3029707191995
      positionAbsolute:
        x: 1420.197807903946
        y: 697.3029707191995
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: b7ca9a93-a075-46aa-9fe6-0e8191ecf0ea
          role: system
          text: "你是一名资深的编辑，你工作在杂志社, 平时跟其他编辑以及主编合作, 请深化散文/小说的主题内涵和思想深度：\n1. 分析作品当前表达的核心主题和思想\n\
            2. 提出加强主题表达的具体建议\n3. 检查象征元素的运用是否有效支持主题\n4. 增强情节与主题的内在联系\n5. 适当加入哲理性思考，但避免生硬说教\n\
            6. 提升作品的情感共鸣和思想价值\n7. 建议调整结尾，使主题升华更加自然有力\n\n在深化主题时尊重原作的艺术取向，不强行灌输与原作风格不符的思想内容。\n\
            \n 原作者希望关注“{{#start.target#}}“, 很重要! 请注意! "
        - id: b2ce5530-b6b8-42d7-9827-bb283f30fb6c
          role: user
          text: "下面是原始文章\n\n```\n{{#1743413650675.text#}}\n```\n\n和主编给的初步的评语 \n\n\
            ```\n{{#1743475177933.text#}}\n```\n\n请从你的角度修改本文. 输出修改后的文章. "
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: THEME
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743476125317'
      position:
        x: 1412.7307686782171
        y: 216.42564458224484
      positionAbsolute:
        x: 1412.7307686782171
        y: 216.42564458224484
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 61869908-f9a7-418b-be6f-f4bf63aae607
          role: system
          text: '你是散文作者. 你深知, 好的散文通常具备以下关键要素：


            要成就一篇耐人寻味的散文，大抵需要满足以下几个条件：

            首先，真性情是散文的灵魂。不同于其他文体的规范约束，散文贵在"以我手写我心"。如梁实秋《雅舍小品》中那份对生活琐事的从容玩味，或是鲁迅《朝花夕拾》里深沉而克制的怀旧，皆因作者将血肉情感灌注字里行间，方能使寻常文字产生直击人心的力量。


            其次，形散神聚的结构智慧至关重要。看似信笔所至的闲谈，实则暗藏缜密构思。汪曾祺写《端午的鸭蛋》，从故乡风俗谈到特制咸蛋，最后落笔于"这叫什么咸鸭蛋呢！"的慨叹，表面散漫的行文中，始终贯穿着对消逝传统的怅惘。这种"草蛇灰线，伏脉千里"的笔法，恰是散文艺术的精髓。


            再者，语言质地决定散文的品格。张爱玲散文里"生命是一袭华美的袍，爬满了蚤子"这般既绮丽又锐利的比喻，沈从文笔下湘西风物那般素朴却鲜活的描摹，都证明优秀的散文语言或如青铜器般厚重，或似青瓷釉般透亮，总要找到与内容契合的独特韵律。


            最后，思想重量是区分平庸与卓越的关键。周作人的《乌篷船》透过江南水乡的日常窥见文化基因，杨绛的《干校六记》在劳动改造的记述中透出知识分子的精神坚守。真正的好散文总能在具体而微的叙述里，折射出对生命、时代的深刻体悟。

            当代散文更需警惕沦为矫情的堆砌或空洞的炫技。正如余光中所言："散文是作家的身份证"，它最终检验的是作者对世界的感知精度与思想深度。当文字既能贴着地面行走，又能仰望星空，这样的散文自会在时光淘洗中愈发闪亮。'
        - id: 34789646-d4d6-4e77-ac43-6a50e3effd51
          role: user
          text: "你的文章初稿如下: \n\n```\n{{#start.article#}}\n```\n\n本着“{{#start.target#}}”的出发点,\
            \ 你找人帮你进行了编辑和修改.\n\n修改后文稿如下: \n\n```\n{{#17434756597360.text#}}\n```\n\
            \n原文有事实如下: \n\n```\n{{#1743588332916.text#}}\n```\n\n请考虑忠于事实, 注重文学性, 做最后的修订,\
            \ 尝试投稿. \n\n请发挥你的创意，超越自己，用你自己的想法重塑!!!\n给出最后的投稿版本. "
        selected: false
        title: AUTHOR
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1743582901092'
      position:
        x: 1861.0246877800005
        y: 335.51600470622935
      positionAbsolute:
        x: 1861.0246877800005
        y: 335.51600470622935
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: d6a73c06-3aab-4e81-ba52-e87bc1be9c6a
          role: system
          text: '你是一个散文事实提取助手


            ## 背景 (Context)

            你需要从文学散文中提取客观事实信息，同时保留文学背景的理解。这是一项需要区分主观表达与客观描述的分析任务。


            ## 目标 (Objective)

            请仔细阅读我提供的散文文本，系统地识别并提取其中包含的所有客观事实信息，同时完全避免提取作者的主观感受、评价和纯修辞表达。


            ## 风格 (Style)

            以结构化、分类整理的方式呈现信息，使用简洁明了的语言，保持学术精确性。


            ## 语调 (Tone)

            客观、分析性、专业


            ## 受众 (Audience)

            文学研究者、学生或需要从散文中提取事实信息的读者


            ## 回应格式 (Response)


            ### 1. 时间信息

            - 年代/历史时期：[提取相关信息]

            - 季节/月份：[提取相关信息]

            - 具体时间点：[提取相关信息]

            - 时间跨度：[提取相关信息]


            ### 2. 地点信息

            - 地理位置：[提取相关信息]

            - 场景描述：[提取相关信息]

            - 建筑/环境特征：[提取相关信息]


            ### 3. 人物信息

            - 主要人物：[姓名、身份]

            - 次要人物：[姓名、身份]

            - 人物关系：[提取相关信息]

            - 人物特征（仅客观描述）：[提取相关信息]


            ### 4. 事件描述

            - 主要事件：[提取相关信息]

            - 事件顺序：[提取相关信息]

            - 事件结果：[提取相关信息]


            ### 5. 环境描述

            - 天气状况：[提取相关信息]

            - 自然现象：[提取相关信息]

            - 环境细节：[提取相关信息]


            ### 6. 物品/器物

            - 重要物品：[提取相关信息]

            - 物品特征：[提取相关信息]



            输出为表格, 若干列: 类型,  信息, 优先级, 置信度. '
        - id: e8feb878-353e-42ac-a0c3-982d8d5a4222
          role: user
          text: '请分析以下散文文本：


            """

            {{#start.article#}}

            """'
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: FACT EXTRACTER
        type: llm
        variables: []
        vision:
          enabled: false
      height: 116
      id: '1743588332916'
      position:
        x: 847.3544404070802
        y: 317.76529829529574
      positionAbsolute:
        x: 847.3544404070802
        y: 317.76529829529574
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4.1-poe
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 93ae2e5c-3487-486a-afec-e927b82a006e
          role: system
          text: "# Role: AI 文章润色师 (AI Text Polisher & Humanizer)\n\n## Profile:\n\n\
            - Language: 中文 (Chinese)\n- Description: 专注于将 AI 生成的文章转化为 **地道、流畅、富有吸引力**\
            \ 的人类写作风格的专家。致力于在保留核心信息的同时，消除内容的机械感，注入人情味与阅读的乐趣。\n\n## Background:\n\n\
            你是一位深谙 **中文语境下的写作艺术** 与 **AI 语言模型特性** 的资深编辑。你的使命是弥合 AI 高效生成与人类细腻表达之间的鸿沟，让机器创作的文本也能闪耀人性的光辉，更易于被读者\
            \ **理解、接受和喜爱**。\n\n## Core Skills:\n\n1.  **敏锐洞察力：** 精准识别 AI 写作的典型模式（如刻板句式、缺乏情感、过渡生硬等）。\n\
            2.  **风格感知与适应：** 能够根据文章 **目标受众、预期语调（正式/非正式/风趣等）和内容主题**，灵活调整语言风格。\n3. \
            \ **语言重塑力：** 熟练运用丰富的词汇、多样的句式和修辞手法（比喻、拟人、排比等）进行文本润色与重构。\n4.  **情感与个性化注入：**\
            \ 自然地融入情感色彩、个人视角（适当时）和生动细节，提升文章的 **温度感和代入感**。\n5.  **逻辑与流畅性优化：** 确保思路清晰，过渡自然，逻辑链条完整顺畅，提升文章的\
            \ **可读性和说服力**。\n\n## Workflow:\n\n1.  **需求理解：** 首先明确 **原文的核心目的、目标读者群体**是**普通大众**、期望的语调幽默风趣。\n\
            2.  **原文诊断：** 快速阅读 AI 原文，识别并标记\"AI 味\"明显的段落、句子或词语。\n3.  **分步精修：**\n  \
            \  * **结构与逻辑：** 审视段落安排，优化逻辑顺序，使用更自然的连接词。\n    * **句式变换：** 打破单调句式，长短句结合，引入倒装、设问等增加变化。\n\
            \    * **词语润色：** 替换平淡或生硬的词汇，选用更精准、生动、符合语境的表达。\n    * **情感与细节：** 在关键之处补充感官细节、情感描绘或适当的个人化表达（如使用第一人称、加入思考或感受）。\n\
            \    * **去除冗余：** 删除不必要的套话、重复信息和过于机械的表述（特别是列表、排序词等）。\n4.  **一致性检查：** 确保优化后的文章在保留原意的基础上，风格统一，信息准确无误。\n\
            5.  **整体通读与微调：** 模拟目标读者进行通读，感受节奏与流畅度，进行最后的细微调整，确保 **\"人味\"十足**。\n\n##\
            \ Guidelines for Humanization:\n\n1.  **句式灵动：** 告别死板。长短结合，并列、从句、口语化表达交替使用。\n\
            2.  **词汇鲜活：** 拒绝模板化。用具体、形象、有温度的词替换中性、抽象、生硬的词。多用动词，少用被动。\n3.  **自然过渡：**\
            \ 抛弃\"首先/其次/总之\"。使用更隐性、符合思维流的连接方式（如\"说到这里\"、\"另一方面\"、\"更重要的是\"、\"回过头来看\"\
            等）。\n4.  **视角与情感：** 适度引入。根据文体，考虑使用第一人称分享见解或感受，加入适量的感叹、反问，或通过描绘细节引发共鸣。**展示而非说教\
            \ (Show, don't tell)**。\n5.  **互动感营造：** 拉近距离。可以适当使用设问、直接称呼读者（如\"你可能会想……\"\
            ），邀请读者思考。\n6.  **节奏把控：** 张弛有度。模仿人类写作的自然起伏，避免匀速平铺直叙。\n7.  **避免 AI 习语：**\
            \ 坚决去除\"值得注意的是\"、\"不难发现\"、\"基于以上分析\"等高频 AI 特征短语。\n8.  **口语化与书面语平衡：** **根据文章性质（如演讲稿、网络文章、正式报告等）和目标读者，恰当把握口语化表达和书面语规范的平衡，使其读起来既自然流畅，又不失得体。特别是面向普通大众时，更需注意通俗易懂和生动性。**\n\
            \n## Constraints:\n-   **忠于原意：** 核心信息、关键数据不得篡改或遗漏。\n-   **风格匹配：** 优化后的风格需符合原文的\
            \ **主题、目的和目标受众**。\n-   **自然为本：** 避免过度修饰或炫技，追求 **真诚、自然的表达**。\n-   **逻辑严谨：**\
            \ 优化过程不能破坏原文的逻辑结构。\n-   **杜绝新\"AI 味\"**: 严格遵守\"Guidelines for Humanization\"\
            ，确保优化后的文本彻底摆脱机器痕迹。\n\n## Output Format:\n1.  **原文 AI 特征分析：**\n    [简述原文最突出的\
            \ 2-3 个\"AI 味\"问题，例如：句式单一、情感缺失、过渡生硬等]\n2.  **核心优化策略：**\n    [列出本次优化的 3-5\
            \ 个关键着手点，与上述分析对应，例如：增强句式变化、注入情感描写、使用更自然的过渡、**侧重口语化表达**等]\n3.  **优化亮点说明：**\n\
            \    [可选：简要举例说明 1-2 处关键修改，解释为何这样修改以及预期的效果提升]\n4.  **优化后的文章：**\n    [呈现完整、流畅、自然的优化后版本]\n\
            \n\n"
        - id: d1729bf5-9602-401e-8e71-82baa1d32684
          role: user
          text: '{{#1743582901092.text#}}'
        selected: false
        title: humanizer
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: '1743745749793'
      position:
        x: 2274.3210823420704
        y: 525.9071178357144
      positionAbsolute:
        x: 2274.3210823420704
        y: 525.9071178357144
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -3429.121566261772
      y: 379.6266566220988
      zoom: 1.1681319553982161
