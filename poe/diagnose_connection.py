#!/usr/bin/env python3
"""
Diagnostic script to troubleshoot Poe API connection issues
"""

import asyncio
import os
import sys
import traceback
import socket
import ssl
import httpx
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_basic_connectivity():
    """Test basic network connectivity"""
    print("=== Testing Basic Network Connectivity ===")
    
    # Test DNS resolution
    try:
        host = "api.poe.com"
        ip = socket.gethostbyname(host)
        print(f"✓ DNS resolution successful: {host} -> {ip}")
    except Exception as e:
        print(f"✗ DNS resolution failed: {e}")
        return False
    
    # Test basic TCP connection
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, 443))
        sock.close()
        if result == 0:
            print(f"✓ TCP connection to {host}:443 successful")
        else:
            print(f"✗ TCP connection to {host}:443 failed")
            return False
    except Exception as e:
        print(f"✗ TCP connection test failed: {e}")
        return False
    
    return True

async def test_tls_connection():
    """Test TLS/SSL connection"""
    print("\n=== Testing TLS/SSL Connection ===")
    
    try:
        context = ssl.create_default_context()
        host = "api.poe.com"
        port = 443
        
        with socket.create_connection((host, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=host) as ssock:
                print(f"✓ TLS connection successful")
                print(f"  Protocol: {ssock.version()}")
                print(f"  Cipher: {ssock.cipher()}")
                cert = ssock.getpeercert()
                print(f"  Certificate subject: {cert.get('subject', 'N/A')}")
                return True
    except Exception as e:
        print(f"✗ TLS connection failed: {e}")
        traceback.print_exc()
        return False

async def test_httpx_connection():
    """Test HTTPX connection with different configurations"""
    print("\n=== Testing HTTPX Connection ===")
    
    # Test with default settings
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get("https://api.poe.com")
            print(f"✓ HTTPX default connection successful: {response.status_code}")
    except Exception as e:
        print(f"✗ HTTPX default connection failed: {e}")
    
    # Test with custom SSL context
    try:
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        async with httpx.AsyncClient(
            timeout=30.0,
            verify=ssl_context
        ) as client:
            response = await client.get("https://api.poe.com")
            print(f"✓ HTTPX with custom SSL context successful: {response.status_code}")
    except Exception as e:
        print(f"✗ HTTPX with custom SSL context failed: {e}")
    
    # Test with different HTTP versions
    try:
        async with httpx.AsyncClient(
            timeout=30.0,
            http2=False  # Force HTTP/1.1
        ) as client:
            response = await client.get("https://api.poe.com")
            print(f"✓ HTTPX with HTTP/1.1 successful: {response.status_code}")
    except Exception as e:
        print(f"✗ HTTPX with HTTP/1.1 failed: {e}")

async def test_poe_api():
    """Test actual Poe API connection"""
    print("\n=== Testing Poe API Connection ===")
    
    api_key = os.environ.get("POE_API_KEY")
    if not api_key:
        print("✗ No POE_API_KEY found in environment")
        return False
    
    print(f"Using API key: {api_key[:8]}...")
    
    try:
        import fastapi_poe as fp
        from fastapi_poe.types import ProtocolMessage
        
        # Test with a simple message
        messages = [ProtocolMessage(role="user", content="Hello, can you respond with just 'Hi'?")]
        
        print("Attempting to connect to Gemini-2.5-Pro...")
        
        # Try with timeout and error handling
        response_received = False
        async for partial_response in fp.get_bot_response(
            messages=messages,
            bot_name="Gemini-2.5-Pro",
            api_key=api_key
        ):
            print(f"✓ Received response from Poe API: {type(partial_response)}")
            if hasattr(partial_response, 'text'):
                print(f"  Text: {partial_response.text[:100]}...")
            if hasattr(partial_response, 'text_delta'):
                print(f"  Text delta: {partial_response.text_delta[:100]}...")
            response_received = True
            break  # Just test the first response
        
        if response_received:
            print("✓ Poe API connection successful")
            return True
        else:
            print("✗ No response received from Poe API")
            return False
            
    except Exception as e:
        print(f"✗ Poe API connection failed: {e}")
        traceback.print_exc()
        return False

async def test_proxy_settings():
    """Check for proxy settings that might interfere"""
    print("\n=== Checking Proxy Settings ===")
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    proxy_found = False
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"Found proxy setting: {var}={value}")
            proxy_found = True
    
    if not proxy_found:
        print("✓ No proxy settings found")
    else:
        print("⚠ Proxy settings detected - these might interfere with connections")

async def main():
    """Run all diagnostic tests"""
    print("Poe API Connection Diagnostic Tool")
    print("=" * 50)
    
    # Run all tests
    await test_basic_connectivity()
    await test_tls_connection()
    await test_httpx_connection()
    await test_proxy_settings()
    await test_poe_api()
    
    print("\n" + "=" * 50)
    print("Diagnostic complete. Check the results above for any issues.")

if __name__ == "__main__":
    asyncio.run(main())
