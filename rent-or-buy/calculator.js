let currentChart = null;
let npvTrendChart = null;

module.exports = {
    calculateMortgageNPV,
    calculateRentNPV,
    calculateLoanDetails,
    findThreshold
};

function validateInputs() {
    const inputs = {
        housePrice: { min: 1, max: 100000, message: '请输入有效的房屋总价' },
        years: { min: 1, max: 100, message: '请输入有效的持有年数' },
        appreciation: { min: -20, max: 20, message: '请输入有效的年增值率' },
        rentPrice: { min: 0.1, max: 1000, message: '请输入有效的年租金' },
        rentGrowth: { min: -20, max: 20, message: '请输入有效的租金增长率' },
        downPayment: { min: 0, max: 100, message: '请输入有效的首付比例' },
        loanRate: { min: 0, max: 20, message: '请输入有效的贷款利率' },
        loanYears: { min: 1, max: 100, message: '请输入有效的贷款年限' },
        maintenance: { min: 0, max: 10, message: '请输入有效的维护费率' },
        investmentReturn: { min: -20, max: 20, message: '请输入有效的投资收益率' },
        transactionCost: { min: 0, max: 100, message: '请输入有效的交易成本' }
    };

    let isValid = true;
    let errorMessages = [];

    // 基本范围验证
    for (const [id, config] of Object.entries(inputs)) {
        const input = document.getElementById(id);
        const value = parseFloat(input.value);
        const error = document.getElementById(`${id}Error`);

        if (isNaN(value) || value < config.min || value > config.max) {
            error.style.display = 'block';
            error.textContent = config.message;
            isValid = false;
            errorMessages.push(config.message);
        } else {
            error.style.display = 'none';
        }
    }

    // 额外的逻辑验证
    const years = parseInt(document.getElementById('years').value);
    const loanYears = parseInt(document.getElementById('loanYears').value);
    const loanYearsError = document.getElementById('loanYearsError');

    // 贷款年限不应超过持有年数
    if (loanYears > years && isValid) {
        loanYearsError.style.display = 'block';
        loanYearsError.textContent = '贷款年限不应超过持有年数';
        isValid = false;
        errorMessages.push('贷款年限不应超过持有年数');
    }

    // 租金合理性检查
    const housePrice = parseFloat(document.getElementById('housePrice').value) * 10000;
    const rentPrice = parseFloat(document.getElementById('rentPrice').value) * 10000;
    const rentPriceError = document.getElementById('rentPriceError');

    // 租售比检查 (年租金/房价)
    const rentToPriceRatio = (rentPrice / housePrice) * 100;
    if (rentToPriceRatio > 10 && isValid) {
        rentPriceError.style.display = 'block';
        rentPriceError.textContent = `租售比(${rentToPriceRatio.toFixed(2)}%)异常高，请确认租金输入是否正确`;
        isValid = false;
        errorMessages.push(`租售比(${rentToPriceRatio.toFixed(2)}%)异常高，请确认租金输入是否正确`);
    } else if (rentToPriceRatio < 0.5 && isValid) {
        rentPriceError.style.display = 'block';
        rentPriceError.textContent = `租售比(${rentToPriceRatio.toFixed(2)}%)异常低，请确认租金输入是否正确`;
        isValid = false;
        errorMessages.push(`租售比(${rentToPriceRatio.toFixed(2)}%)异常低，请确认租金输入是否正确`);
    }

    if (!isValid && errorMessages.length > 0) {
        console.warn('输入验证失败:', errorMessages);
    }

    return isValid;
}

function calculate() {
    if (!validateInputs()) {
        alert('请检查输入参数是否正确!');
        return;
    }

    // 获取输入值(注意单位转换:万元->元)
    const P0 = parseFloat(document.getElementById('housePrice').value) * 10000;
    const T = parseInt(document.getElementById('years').value);
    const g = parseFloat(document.getElementById('appreciation').value) / 100;
    const R0 = parseFloat(document.getElementById('rentPrice').value) * 10000;
    const gR = parseFloat(document.getElementById('rentGrowth').value) / 100;
    const k = parseFloat(document.getElementById('downPayment').value) / 100;
    const rm = parseFloat(document.getElementById('loanRate').value) / 100;
    const n = parseInt(document.getElementById('loanYears').value);
    const m = parseFloat(document.getElementById('maintenance').value) / 100;
    const d = parseFloat(document.getElementById('investmentReturn').value) / 100;
    const transactionCost = parseFloat(document.getElementById('transactionCost').value) / 100;
    const paymentType = document.getElementById('paymentType').value;

    // 计算贷款购房NPV
    let result_mortgage = calculateMortgageNPV(P0, T, g, k, rm, n, m, d, paymentType, transactionCost);
    let NPV_mortgage = result_mortgage.finalNPV;

    // 计算租房NPV
    let result_rent = calculateRentNPV(P0, R0, T, gR, d);
    let NPV_rent = result_rent.finalNPV;

    // 计算贷款详情
    const loanDetails = calculateLoanDetails(P0, k, rm, n, paymentType);

    // 展示结果
    displayResults(NPV_mortgage, NPV_rent, loanDetails, T, result_mortgage, result_rent);
    
    // 计算敏感性分析并展示结果
    const sensitivityResults = calculateSensitivity(P0, T, g, R0, gR, k, rm, n, m, d, paymentType, transactionCost);
    displaySensitivityResults(sensitivityResults);
    
    // 显示结果区域
    document.getElementById('results').style.display = 'block';
}

function calculateLoanDetails(P0, k, rm, n, paymentType) {
    const loanAmount = P0 * (1 - k);
    const monthlyRate = rm / 12;
    const totalMonths = n * 12;
    const loanDetails = {
        amount: loanAmount,
        monthlyRate: monthlyRate,
        totalMonths: totalMonths,
        paymentType: paymentType
    };

    if (paymentType === 'equal_installment') {
        const monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, totalMonths))
                                / (Math.pow(1 + monthlyRate, totalMonths) - 1);
        
        const firstMonthInterest = loanAmount * monthlyRate;
        const firstMonthPrincipal = monthlyPayment - firstMonthInterest;
        
        const totalPayment = monthlyPayment * totalMonths;
        const totalInterest = totalPayment - loanAmount;
        
        loanDetails.monthlyPayment = monthlyPayment;
        loanDetails.totalPayment = Number(totalPayment.toFixed(2));
        loanDetails.totalInterest = Number(totalInterest.toFixed(2));
        loanDetails.firstMonthInterest = firstMonthInterest * 12;
        loanDetails.firstMonthPrincipal = firstMonthPrincipal * 12;
    } else {
        const yearlyPrincipal = loanAmount / n; // Yearly principal payment
        const monthlyPrincipalAmount = yearlyPrincipal / 12; // Monthly principal payment
        const firstMonthInterest = loanAmount * monthlyRate;
        const firstMonthPayment = monthlyPrincipalAmount + firstMonthInterest;
        const lastMonthInterest = monthlyPrincipalAmount * monthlyRate;
        const lastMonthPayment = monthlyPrincipalAmount + lastMonthInterest;
        const totalInterest = (loanAmount * (totalMonths + 1) * monthlyRate) / 2;

        loanDetails.monthlyPrincipal = yearlyPrincipal; // Store as yearly amount for display
        loanDetails.totalPayment = Number((loanAmount + totalInterest).toFixed(2));
        loanDetails.totalInterest = Number(totalInterest.toFixed(2));
        loanDetails.firstMonthPayment = firstMonthPayment * 12; // Convert to yearly for display
        loanDetails.lastMonthPayment = lastMonthPayment * 12; // Convert to yearly for display
    }

    return loanDetails;
}

function calculateMortgageNPV(P0, T, g, k, rm, n, m, d, paymentType, transactionCost) {
    const downPayment = k * P0;
    const loanAmount = P0 * (1 - k);
    const monthlyRate = rm / 12;
    const totalMonths = n * 12;
    let yearlyNPV = new Array(T + 1);
    yearlyNPV[0] = -downPayment;
    
    if (paymentType === 'equal_installment') {
        const monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, totalMonths))
                            / (Math.pow(1 + monthlyRate, totalMonths) - 1);

        let remainingPrincipal = loanAmount;
        for (let t = 1; t <= T; t++) {
            yearlyNPV[t] = yearlyNPV[t-1];

            if (t <= n) {
                let yearlyPayment = 0;
                for (let month = 1; month <= 12 && remainingPrincipal > 0; month++) {
                    const monthlyInterest = remainingPrincipal * monthlyRate;
                    const monthlyPrincipalPaid = Math.min(monthlyPayment - monthlyInterest, remainingPrincipal);
                    yearlyPayment += monthlyPrincipalPaid + monthlyInterest;
                    remainingPrincipal = Math.max(0, remainingPrincipal - monthlyPrincipalPaid);
                }
                yearlyNPV[t] -= yearlyPayment / Math.pow(1 + d, t);
            }
            
            yearlyNPV[t] -= (m * P0) / Math.pow(1 + d, t);
            
            if (t === T) {
                const finalValue = P0 * Math.pow(1 + g, T) * (1 - transactionCost);
                yearlyNPV[t] += (finalValue - Math.max(0, remainingPrincipal)) / Math.pow(1 + d, T);
            }
        }
    } else {
        const monthlyPrincipal = loanAmount / totalMonths;
        let remainingPrincipal = loanAmount;
        
        for (let t = 1; t <= T; t++) {
            yearlyNPV[t] = yearlyNPV[t-1];
            
            if (t <= n) {
                const yearStartPrincipal = remainingPrincipal;
                const yearPrincipal = Math.min(monthlyPrincipal * 12, remainingPrincipal);
                const avgPrincipal = (yearStartPrincipal + (yearStartPrincipal - yearPrincipal)) / 2;
                const yearInterest = avgPrincipal * rm;
                const yearlyPayment = yearPrincipal + yearInterest;
                
                remainingPrincipal = Math.max(0, remainingPrincipal - yearPrincipal);
                yearlyNPV[t] -= yearlyPayment / Math.pow(1 + d, t);
            }
            
            yearlyNPV[t] -= (m * P0) / Math.pow(1 + d, t);
            
            if (t === T) {
                const finalValue = P0 * Math.pow(1 + g, T) * (1 - transactionCost);
                yearlyNPV[t] += (finalValue - Math.max(0, remainingPrincipal)) / Math.pow(1 + d, T);
            }
        }
    }
    
    return {
        finalNPV: yearlyNPV[T],
        yearlyNPV: yearlyNPV
    };
}

function calculateRentNPV(P0, R0, T, gR, d) {
    let yearlyNPV = new Array(T + 1);
    yearlyNPV[0] = P0;
    
    for (let t = 1; t <= T; t++) {
        yearlyNPV[t] = yearlyNPV[t-1];
        const yearlyRent = R0 * Math.pow(1 + gR, t - 1);
        yearlyNPV[t] -= yearlyRent / Math.pow(1 + d, t);
    }
    
    return {
        finalNPV: yearlyNPV[T],
        yearlyNPV: yearlyNPV
    };
}

function findThreshold(min, max, step, maxIterations, evaluator) {
    const tolerance = 1e-6;
    let left = min;
    let right = max;
    let iterations = 0;
    let bestThreshold = null;
    let minDiff = Infinity;

    // Binary search
    while (iterations < maxIterations && (right - left) > tolerance) {
        const mid = (left + right) / 2;
        const value = evaluator(mid);

        if (Math.abs(value) < minDiff) {
            minDiff = Math.abs(value);
            bestThreshold = mid;
        }

        if (Math.abs(value) < tolerance) {
            return mid;
        }

        // For x^2 - 4 = 0, we want to find x = 2 (positive root)
        // So when value > 0, we should move left
        if (value > 0) {
            right = mid;
        } else {
            left = mid;
        }

        iterations++;
    }

    return bestThreshold;
}

function calculateSensitivity(P0, T, g, R0, gR, k, rm, n, m, d, paymentType, transactionCost) {
    const results = [];
    const step = 0.00001; // Increased precision for calculations
    const maxIterations = 200000; // Increased max iterations for better convergence

    // Helper function to format threshold descriptions
    const getThresholdDescription = (param, current, threshold, higherIsBetter) => {
        const diff = Math.abs(threshold - current);
        const direction = threshold > current ? '高于' : '低于';
        const better = higherIsBetter ? (threshold > current ? '贷款购房' : '租房') : (threshold > current ? '租房' : '贷款购房');
        const sensitivity = diff < 0.01 ? '极其敏感' : diff < 0.03 ? '较为敏感' : '相对稳定';
        return `当${param}${direction}${(threshold * 100).toFixed(2)}%时,${better}更优 (对${param}${sensitivity})`;
    };

    // Helper function to calculate NPV difference for a given parameter change
    const calculateNPVDifference = (paramName, testValue) => {
        let testP0 = P0, testT = T, testG = g, testR0 = R0, testGR = gR, testK = k, testRm = rm, testN = n, testM = m, testD = d, testTC = transactionCost;

        switch(paramName) {
            case 'investmentReturn': testD = testValue; break;
            case 'appreciation': testG = testValue; break;
            case 'rentGrowth': testGR = testValue; break;
            case 'loanRate': testRm = testValue; break;
            case 'downPayment': testK = testValue; break;
            case 'transactionCost': testTC = testValue; break;
        }

        const mortgageNPV = calculateMortgageNPV(testP0, testT, testG, testK, testRm, testN, testM, testD, paymentType, testTC).finalNPV;
        const rentNPV = calculateRentNPV(testP0, testR0, testT, testGR, testD).finalNPV;
        return mortgageNPV - rentNPV;
    };

    // Define sensitivity analysis parameters
    const sensitivityParams = [
        {
            name: 'investmentReturn',
            displayName: '投资收益率',
            currentValue: d,
            minRange: Math.max(0.01, d - 0.2),
            maxRange: Math.min(0.4, d + 0.2),
            maxDiff: 0.4,
            higherIsBetter: true
        },
        {
            name: 'appreciation',
            displayName: '房价年增值率',
            currentValue: g,
            minRange: Math.max(-0.2, g - 0.2),
            maxRange: Math.min(0.4, g + 0.2),
            maxDiff: 0.4,
            higherIsBetter: false
        },
        {
            name: 'rentGrowth',
            displayName: '租金年增长率',
            currentValue: gR,
            minRange: Math.max(-0.2, gR - 0.2),
            maxRange: Math.min(0.4, gR + 0.2),
            maxDiff: 0.4,
            higherIsBetter: true
        },
        {
            name: 'loanRate',
            displayName: '贷款年利率',
            currentValue: rm,
            minRange: Math.max(0.01, rm - 0.1),
            maxRange: Math.min(0.2, rm + 0.1),
            maxDiff: 0.15,
            higherIsBetter: true
        },
        {
            name: 'downPayment',
            displayName: '首付比例',
            currentValue: k,
            minRange: 0.2,
            maxRange: 0.8,
            maxDiff: 1.0,
            higherIsBetter: true
        },
        {
            name: 'transactionCost',
            displayName: '交易成本',
            currentValue: transactionCost,
            minRange: Math.max(0, transactionCost - 0.05),
            maxRange: Math.min(0.15, transactionCost + 0.05),
            maxDiff: 0.1,
            higherIsBetter: true
        }
    ];

    // Calculate thresholds for each parameter
    sensitivityParams.forEach(param => {
        const threshold = findThreshold(
            param.minRange,
            param.maxRange,
            step,
            maxIterations,
            (testValue) => calculateNPVDifference(param.name, testValue)
        );

        if (threshold !== null && Math.abs(threshold - param.currentValue) < param.maxDiff) {
            // Additional validation for downPayment
            if (param.name === 'downPayment' && (threshold < 0.2 || threshold > 0.8)) {
                return; // Skip invalid down payment ratios
            }

            results.push({
                parameter: param.displayName,
                currentValue: param.currentValue,
                threshold: threshold,
                description: getThresholdDescription(param.displayName, param.currentValue, threshold, param.higherIsBetter)
            });
        }
    });



    return results;
}

function displaySensitivityResults(results) {
    const sensitivityResults = document.getElementById('sensitivityResults');
    sensitivityResults.innerHTML = `
        <div class="card">
            <div class="card-body">
                <h5 class="card-title mb-4">敏感性分析结果</h5>
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle"></i>
                    敏感性分析显示了各个参数的临界值，当参数超过临界值时会改变租房与买房的优劣判断。
                    参数对结果的敏感程度反映了决策的稳定性。
                </div>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>影响因素</th>
                                <th>当前值</th>
                                <th>临界值</th>
                                <th>敏感程度</th>
                                <th>分析结论</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${results.map(item => {
                                const diff = Math.abs(item.threshold - item.currentValue);
                                const sensitivityClass = diff < 0.01 ? 'danger' : diff < 0.03 ? 'warning' : 'success';
                                const sensitivityText = diff < 0.01 ? '极其敏感' : diff < 0.03 ? '较为敏感' : '相对稳定';
                                return `
                                    <tr>
                                        <td><strong>${item.parameter}</strong></td>
                                        <td>${(item.currentValue * 100).toFixed(2)}%</td>
                                        <td>${(item.threshold * 100).toFixed(2)}%</td>
                                        <td><span class="badge bg-${sensitivityClass}">${sensitivityText}</span></td>
                                        <td>${item.description}</td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

function displayResults(NPV_mortgage, NPV_rent, loanDetails, T, result_mortgage, result_rent) {
    if (currentChart) {
        currentChart.destroy();
    }
    if (npvTrendChart) {
        npvTrendChart.destroy();
    }
    
    function formatMoney(amount) {
        return (amount / 10000).toFixed(2) + '万元';
    }
    
    function calculateROI(npv, initialInvestment) {
        const years = parseInt(document.getElementById('years').value);
        const roi = Math.pow((npv + initialInvestment) / initialInvestment, 1/years) - 1;
        return (roi * 100).toFixed(2) + '%';
    }
    
    const housePrice = parseFloat(document.getElementById('housePrice').value) * 10000;
    const downPayment = housePrice * parseFloat(document.getElementById('downPayment').value) / 100;
    const g = parseFloat(document.getElementById('appreciation').value) / 100;
    const d = parseFloat(document.getElementById('investmentReturn').value) / 100;
    
    const finalHouseValue = housePrice * Math.pow(1 + g, T);
    const finalHouseValueAfterCost = finalHouseValue * 0.95;
    const discountedFinalValue = finalHouseValueAfterCost / Math.pow(1 + d, T);
    
    const mortgageResult = document.getElementById('mortgageResult');
    let mortgageContent = `
        <p>净现值(NPV): ${formatMoney(NPV_mortgage)}</p>
        <p>年化回报率: ${calculateROI(NPV_mortgage, downPayment)}</p>
        <p class="mt-3"><strong>房屋价值变化:</strong></p>
        <p>- 初始价值: ${formatMoney(housePrice)}</p>
        <p>- ${T}年后价值: ${formatMoney(finalHouseValue)} <small class="text-muted">(年增值率${(g*100).toFixed(1)}%)</small></p>
        <p>- 扣除交易成本: ${formatMoney(finalHouseValueAfterCost)} <small class="text-muted">(5%交易成本)</small></p>
        <p>- 折现到当前: ${formatMoney(discountedFinalValue)} <small class="text-muted">(折现率${(d*100).toFixed(1)}%)</small></p>
        <p class="mt-3"><strong>贷款明细:</strong></p>
        <p>首付金额: ${formatMoney(downPayment)}</p>
        <p>贷款金额: ${formatMoney(loanDetails.amount)}</p>
        <p>总支付利息: ${formatMoney(loanDetails.totalInterest)}</p>
    `;
    
    if (loanDetails.paymentType === 'equal_installment') {
        mortgageContent += `
            <p>月供金额: ${formatMoney(loanDetails.monthlyPayment * 12)}/年 (${(loanDetails.monthlyPayment/10000).toFixed(2)}万元/月)</p>
            <p>首年利息: ${formatMoney(loanDetails.firstMonthInterest)}/年 (${(loanDetails.firstMonthInterest/12/10000).toFixed(2)}万元/月)</p>
            <p>首年本金: ${formatMoney(loanDetails.firstMonthPrincipal)}/年 (${(loanDetails.firstMonthPrincipal/12/10000).toFixed(2)}万元/月)</p>
        `;
    } else {
        mortgageContent += `
            <p>月供本金: ${formatMoney(loanDetails.monthlyPrincipal)}/年 (${(loanDetails.monthlyPrincipal/12/10000).toFixed(2)}万元/月)</p>
            <p>首年月供: ${formatMoney(loanDetails.firstMonthPayment)}/年 (${(loanDetails.firstMonthPayment/12/10000).toFixed(2)}万元/月)</p>
            <p>末年月供: ${formatMoney(loanDetails.lastMonthPayment)}/年 (${(loanDetails.lastMonthPayment/12/10000).toFixed(2)}万元/月)</p>
        `;
    }
    
    mortgageResult.querySelector('.result-content').innerHTML = mortgageContent;
    
    const rentResult = document.getElementById('rentResult');
    const yearlyRent = parseFloat(document.getElementById('rentPrice').value) * 10000;
    const rentToPriceRatio = ((yearlyRent / housePrice) * 100).toFixed(2);
    const finalYearRent = yearlyRent * Math.pow(1 + parseFloat(document.getElementById('rentGrowth').value) / 100, T);
    const finalRentToPriceRatio = ((finalYearRent / finalHouseValue) * 100).toFixed(2);

    rentResult.querySelector('.result-content').innerHTML = `
        <p>净现值(NPV): ${formatMoney(NPV_rent)}</p>
        <p>年化回报率: ${calculateROI(NPV_rent, housePrice)}</p>
        <p>初始资金: ${formatMoney(housePrice)}</p>
        <p>首年租金: ${formatMoney(yearlyRent)}/年</p>
        <p class="mt-3"><strong>租售比分析:</strong></p>
        <p>- 首年租售比: ${rentToPriceRatio}% <small class="text-muted">(年租金/房价)</small></p>
        <p>- ${T}年后租售比: ${finalRentToPriceRatio}% <small class="text-muted">(考虑租金增长和房价增值)</small></p>
    `;
    
    const results = [
        { type: 'mortgage', npv: NPV_mortgage, element: mortgageResult },
        { type: 'rent', npv: NPV_rent, element: rentResult }
    ];
    
    results.forEach(r => r.element.classList.remove('better'));
    const bestResult = results.reduce((a, b) => a.npv > b.npv ? a : b);
    bestResult.element.classList.add('better');
    
    const ctx = document.getElementById('resultChart').getContext('2d');
    currentChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['贷款购房', '租房'],
            datasets: [{
                label: '净现值(NPV)',
                data: [NPV_mortgage, NPV_rent].map(v => v / 10000),
                backgroundColor: [
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(75, 192, 192, 0.2)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(75, 192, 192, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '净现值(万元)'
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: '各方案净现值(NPV)对比'
                }
            }
        }
    });

    const trendCtx = document.getElementById('npvTrendChart').getContext('2d');
    const years = Array.from({length: T + 1}, (_, i) => i);
    npvTrendChart = new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: years,
            datasets: [
                {
                    label: '贷款购房',
                    data: result_mortgage.yearlyNPV.map(v => v / 10000),
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    fill: false,
                    tension: 0.4
                },
                {
                    label: '租房',
                    data: result_rent.yearlyNPV.map(v => v / 10000),
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    fill: false,
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '年份'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '累计净现值(万元)'
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: 'NPV逐年变化趋势'
                }
            }
        }
    });
}