<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>租房 vs 贷款买房计算器 - 测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { background-color: #f5f5f5; padding: 20px; }
        .container { background-color: white; border-radius: 10px; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1); margin-bottom: 20px; max-width: 1200px; }
        .form-group { margin-bottom: 15px; position: relative; }
        .input-group-text { min-width: 120px; }
        .result-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 20px; }
        .result-card.better { border-color: #28a745; background-color: #f8fff8; }
        .tooltip-icon { cursor: help; color: #6c757d; margin-left: 5px; font-size: 14px; }
        #resultChart { margin-top: 20px; max-height: 400px; }
        .error-message { color: #dc3545; font-size: 0.875rem; margin-top: 5px; display: none; }
        .input-group input { text-align: right; }
        .model-info { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; display: none; }
        .formula { font-family: "Courier New", monospace; background-color: #fff; padding: 10px; border-radius: 4px; margin: 10px 0; overflow-x: auto; }
        .tooltip-text { display: none; position: absolute; background: #333; color: #fff; padding: 5px 10px; border-radius: 4px; font-size: 12px; z-index: 1000; width: 200px; top: 100%; left: 50%; transform: translateX(-50%); }
        .form-group:hover .tooltip-text { display: block; }
        .payment-type-info { font-size: 0.9em; color: #666; margin-top: 5px; }
        .model-info-toggle { cursor: pointer; color: #0d6efd; text-decoration: underline; margin-bottom: 15px; display: inline-block; }
        .model-info-toggle:hover { color: #0a58ca; }
        .sensitivity-analysis { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px; }
        .threshold-value { font-weight: bold; color: #0d6efd; }
        .result-content { font-size: 0.95em; line-height: 1.6; }
        .result-content p { margin-bottom: 8px; }
        canvas { margin: 20px 0; }
        
        /* 测试相关样式 */
        .test-panel { 
            background-color: #e3f2fd; 
            border: 2px solid #2196f3; 
            border-radius: 10px; 
            padding: 20px; 
            margin-bottom: 20px; 
        }
        .test-controls { margin-bottom: 20px; }
        .test-log { 
            background-color: #000; 
            color: #00ff00; 
            font-family: 'Courier New', monospace; 
            padding: 15px; 
            border-radius: 5px; 
            height: 300px; 
            overflow-y: auto; 
            font-size: 12px;
            white-space: pre-wrap;
        }
        .btn-test { margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">租房 vs 贷款买房计算器 - 测试版</h1>
        
        <!-- 测试控制面板 -->
        <div class="test-panel">
            <h3>🧪 自动化测试控制面板</h3>
            <div class="test-controls">
                <button class="btn btn-primary btn-test" onclick="runFullTest()">🚀 运行完整测试</button>
                <button class="btn btn-secondary btn-test" onclick="testValidation()">📝 测试输入验证</button>
                <button class="btn btn-info btn-test" onclick="testCalculations()">🧮 测试计算功能</button>
                <button class="btn btn-success btn-test" onclick="testSensitivity()">📈 测试敏感性分析</button>
                <button class="btn btn-warning btn-test" onclick="clearLog()">🗑️ 清空日志</button>
            </div>
            <div class="test-log" id="testLog">等待测试开始...\n</div>
        </div>
        
        <div class="text-center mb-3">
            <span id="modelInfoToggle" class="model-info-toggle" onclick="toggleModelInfo()">点击查看计算模型说明 ▼</span>
        </div>

        <div class="model-info" id="modelInfo">
            <h5>计算模型说明</h5>
            <p>本计算器基于现金流量折现(NPV)模型,通过比较不同方案的净现值来评估经济效益。计算中已充分考虑房屋价值因素:</p>
            
            <ul class="mb-3">
                <li>贷款购房方案:计入了房屋增值收益(考虑交易成本)和维护成本</li>
                <li>租房方案:初始资金(等同房价)可获得投资收益</li>
            </ul>
            
            <h6>贷款购房NPV计算公式:</h6>
            <div class="formula">NPV_mortgage = -k·P0 - Σt=1T[PMTt/(1+d)t] - Σt=1T[m·P0/(1+d)t] + [(1-c)·P0·(1+g)T - B_T]/(1+d)T</div>
            <p class="payment-type-info">
                其中PMTt为第t年的还款总额:<br>
                - 等额本息: 每月还款额相同<br>
                - 等额本金: 每月本金相同,总还款额逐月递减
            </p>
            
            <h6>租房NPV计算公式:</h6>
            <div class="formula">NPV_rent = P0 - Σt=1T[R0·(1+g_R)t−1/(1+d)t]</div>
            
            <p class="mt-2">其中:</p>
            <ul>
                <li>P0:房屋购买价格</li>
                <li>T:持有年数</li>
                <li>g:房屋年增值率</li>
                <li>m:年维护费率</li>
                <li>d:投资收益率(贴现率)</li>
                <li>k:首付比例</li>
                <li>PMTt:第t年还款总额</li>
                <li>B_T:T年后剩余贷款本金</li>
                <li>R0:首年租金</li>
                <li>g_R:租金年增长率</li>
                <li>c:房屋交易成本(设为5%)</li>
            </ul>
            
            <p class="mt-2"><strong>特别说明:</strong> 贷款购房方案中,(1-c)·P0·(1+g)T 表示持有期结束时的房屋价值(扣除交易成本)。这部分收益减去剩余贷款本金后,构成购房方案的最终收益。而租房方案中,初始资金P0可以获得稳定的投资收益。</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">基础参数</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">房屋总价 (P0)</span>
                                <input type="number" class="form-control" id="housePrice" value="800" required>
                                <span class="input-group-text">万元</span>
                            </div>
                            <div class="tooltip-text">房屋的当前市场价格(单位:万元)</div>
                            <div class="error-message" id="housePriceError">请输入有效的房屋总价</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">持有年数 (T)</span>
                                <input type="number" class="form-control" id="years" value="30" required>
                                <span class="input-group-text">年</span>
                            </div>
                            <div class="tooltip-text">计划持有房产或租房的时间长度</div>
                            <div class="error-message" id="yearsError">请输入有效的持有年数</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">房屋年增值率 (g)</span>
                                <input type="number" class="form-control" id="appreciation" value="3" step="0.1" required>
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="tooltip-text">预期的房屋每年价值增长率,可为负值</div>
                            <div class="error-message" id="appreciationError">请输入有效的年增值率</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">年租金 (R0)</span>
                                <input type="number" class="form-control" id="rentPrice" value="12" required>
                                <span class="input-group-text">万元</span>
                            </div>
                            <div class="tooltip-text">首年的租金总额(单位:万元/年)</div>
                            <div class="error-message" id="rentPriceError">请输入有效的年租金</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">租金年增长率</span>
                                <input type="number" class="form-control" id="rentGrowth" value="5" step="0.1" required>
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="tooltip-text">预期的租金每年增长率</div>
                            <div class="error-message" id="rentGrowthError">请输入有效的租金增长率</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">维护费率</span>
                                <input type="number" class="form-control" id="maintenance" value="0.1" step="0.1" required>
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="tooltip-text">每年房屋维护费用占房价的比例</div>
                            <div class="error-message" id="maintenanceError">请输入有效的维护费率</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">投资收益率</span>
                                <input type="number" class="form-control" id="investmentReturn" value="2" step="0.1" required>
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="tooltip-text">资金投资的预期年化收益率(作为贴现率)</div>
                            <div class="error-message" id="investmentReturnError">请输入有效的投资收益率</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">交易成本</span>
                                <input type="number" class="form-control" id="transactionCost" value="0.5" step="0.1" required>
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="tooltip-text">房屋买卖时的交易成本占房价比例</div>
                            <div class="error-message" id="transactionCostError">请输入有效的交易成本</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">贷款参数</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">还款方式</span>
                                <select class="form-control" id="paymentType">
                                    <option value="equal_installment">等额本息</option>
                                    <option value="equal_principal">等额本金</option>
                                </select>
                            </div>
                            <div class="tooltip-text">选择贷款的还款方式</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">首付比例</span>
                                <input type="number" class="form-control" id="downPayment" value="25" step="0.1" required>
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="tooltip-text">购房时需要支付的首付款比例</div>
                            <div class="error-message" id="downPaymentError">请输入有效的首付比例</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">贷款年利率</span>
                                <input type="number" class="form-control" id="loanRate" value="3.3" step="0.1" required>
                                <span class="input-group-text">%</span>
                            </div>
                            <div class="tooltip-text">银行贷款的年利率</div>
                            <div class="error-message" id="loanRateError">请输入有效的贷款利率</div>
                        </div>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-text">贷款年限</span>
                                <input type="number" class="form-control" id="loanYears" value="30" required>
                                <span class="input-group-text">年</span>
                            </div>
                            <div class="tooltip-text">贷款的还款期限</div>
                            <div class="error-message" id="loanYearsError">请输入有效的贷款年限</div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mb-4">
            <button id="calculateBtn" class="btn btn-primary btn-lg" onclick="calculate()">计算比较</button>
        </div>

        <div id="results" style="display: none;">
            <div class="text-center mb-3">
                <span id="sensitivityToggle" class="model-info-toggle" onclick="toggleSensitivityAnalysis()">点击查看敏感性分析 ▼</span>
            </div>
            
            <div class="sensitivity-analysis mb-4" id="sensitivityAnalysis" style="display: none;">
                <h5>敏感性分析</h5>
                <p>以下参数的变化会导致最优方案发生改变:</p>
                <div id="sensitivityResults"></div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="result-card" id="mortgageResult">
                        <h5>贷款购房方案</h5>
                        <div class="result-content"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="result-card" id="rentResult">
                        <h5>租房方案</h5>
                        <div class="result-content"></div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <canvas id="resultChart"></canvas>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <canvas id="npvTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleModelInfo() {
            const modelInfo = document.getElementById('modelInfo');
            const toggle = document.getElementById('modelInfoToggle');
            if (modelInfo.style.display === 'none' || !modelInfo.style.display) {
                modelInfo.style.display = 'block';
                toggle.innerHTML = '收起计算模型说明 ▲';
            } else {
                modelInfo.style.display = 'none';
                toggle.innerHTML = '点击查看计算模型说明 ▼';
            }
        }

        function toggleSensitivityAnalysis() {
            const sensitivityAnalysis = document.getElementById('sensitivityAnalysis');
            const toggle = document.getElementById('sensitivityToggle');
            if (sensitivityAnalysis.style.display === 'none' || !sensitivityAnalysis.style.display) {
                sensitivityAnalysis.style.display = 'block';
                toggle.innerHTML = '收起敏感性分析 ▲';
            } else {
                sensitivityAnalysis.style.display = 'none';
                toggle.innerHTML = '点击查看敏感性分析 ▼';
            }
        }
        
        // 测试日志功能
        function logToTest(message) {
            const testLog = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            testLog.textContent += `[${timestamp}] ${message}\n`;
            testLog.scrollTop = testLog.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('testLog').textContent = '日志已清空...\n';
        }
        
        // 重写console.log以同时输出到测试日志
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToTest(args.join(' '));
        };
        
        // 测试控制函数
        async function runFullTest() {
            logToTest('🚀 开始运行完整测试套件...');
            if (window.testRentOrBuy && window.testRentOrBuy.runAllTests) {
                await window.testRentOrBuy.runAllTests();
            } else {
                logToTest('❌ 测试脚本未加载');
            }
        }
        
        function testValidation() {
            logToTest('📝 开始输入验证测试...');
            if (window.testRentOrBuy && window.testRentOrBuy.testInputValidation) {
                window.testRentOrBuy.testInputValidation();
            } else {
                logToTest('❌ 测试脚本未加载');
            }
        }
        
        function testCalculations() {
            logToTest('🧮 开始计算功能测试...');
            if (window.testRentOrBuy && window.testRentOrBuy.testCalculation) {
                const testCase = {
                    name: '手动测试用例',
                    data: {
                        housePrice: 800,
                        years: 30,
                        appreciation: 3,
                        rentPrice: 12,
                        rentGrowth: 5,
                        downPayment: 25,
                        loanRate: 3.3,
                        loanYears: 30,
                        maintenance: 0.1,
                        investmentReturn: 2,
                        transactionCost: 0.5,
                        paymentType: 'equal_installment'
                    }
                };
                window.testRentOrBuy.testCalculation(testCase);
            } else {
                logToTest('❌ 测试脚本未加载');
            }
        }
        
        function testSensitivity() {
            logToTest('📈 开始敏感性分析测试...');
            if (window.testRentOrBuy && window.testRentOrBuy.testSensitivityAnalysis) {
                window.testRentOrBuy.testSensitivityAnalysis();
            } else {
                logToTest('❌ 测试脚本未加载');
            }
        }
    </script>
    <script src="calculator.js"></script>
    <script src="test-ui.js"></script>
</body>
</html>
