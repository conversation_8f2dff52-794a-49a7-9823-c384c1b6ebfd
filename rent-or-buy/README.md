比较贷款买房或者租房的ROI. 

---

## 1. 假设与参数定义

我们先定义一些基本参数（注：参数单位均为“元”或“百分比”表示比例，年化汇率均为复合年利率）：

* 【买房相关参数】
  * P₀：房屋购买价格
  * T：持有年数
  * g：房屋年增值率（假设以复利累计）
  * c：卖房时的交易成本率（例如：5%，则 c = 0.05）
  * m：年维护（及管理、维修）费用率，相对于房屋购买价格（例如：1%，则 m = 0.01）
  * d：贴现率/机会成本（或你期望的替代投资年收益率，例如：5%，则 d = 0.05）
* 【租房相关参数】
  * R₀：首年支付的年租金
  * g_R：租金年增长率（例如：3%，则 g_R = 0.03）
  * 同时，假设如果选择租房，你可以把原本用于购房的大额资金 P₀ 投资，年化收益为 d。
* 【若考虑贷款购房】（下述模型为全款购房的简化模型，贷款版本需构建贷款摊销表）
  * k：首付比例（例如：30%，则 k = 0.3）
  * L = (1 – k)·P₀：贷款金额
  * rₘ：贷款年利率
  * n：贷款总年数（贷款期内的分析若 T < n，应根据 T 年的还款情况计算）
  * 月供 M 的计算公式为：
    M = L × { [rₘ/12]·(1 + rₘ/12)^(12·n) } / { (1 + rₘ/12)^(12·n) – 1 }
  * 贷款持有 T 年后剩余本金 B_T 可用如下公式计算：
    B_T = L·(1 + rₘ/12)^(12·T) – M × { [(1 + rₘ/12)^(12·T) – 1] / (rₘ/12) }
  * T 年末实际可变现（出售后扣除贷款余额和交易成本）的金额为：
    Saleₜ = (1 – c)·P₀·(1 + g)^T – B_T

下面首先给出【全款购房】时的基本模型，再说明如何修正为考虑贷款还款的情况。

---

## 2. 模型一：全款购房 vs. 租房（现金流的NPV比较）

### 2.1 买房的现金流（全款购房）

* t = 0（购买时）：

  初始支出为 P₀（这里忽略交易费已在终结时以交易成本 c 表示）
* t = 1,2, …, T：

  每年支出为维护费： m·P₀
* t = T（出售时）：

  收回金额为：(1 – c)·P₀·(1 + g)^T

将所有现金流以贴现率 d 折现到 t = 0，则买房方案的净现值（NPV_buy）为：

  NPV_buy = – P₀ – Σₜ₌₁^T [ m·P₀ / (1 + d)ᵗ ] + [ (1 – c)·P₀·(1 + g)^T ] / (1 + d)ᵀ

*注：如果你计算的是“成本”而非“净收益”，你可以把买房后最终获得的房款视为正收益。*

---

### 2.2 租房的现金流 + 资金投资收益

假设选择租房后，你不会动用 P₀，而是把这笔资金进行投资，T 年后这笔资金增值为 P₀·(1 + d)^T。同时，每年你需要支付租金，自第1年起，每年租金按 g_R 增长。

注意：为了比较方便，我们可以将 T 年后各项现金流换算为“现值”或者“终值”，两种方法均可。这里给出以终值（Future Value, FV）计算的示例。

* t = T 时：

  资金投资收益：P₀·(1 + d)^T
* 同时，租金费用按“未来值”计算为：

  FV_rent = Σₜ₌₁^T { R₀·(1 + g_R)^(t – 1) × (1 + d)^(T – t) }

因此，选择租房 T 年后净剩余的财富为：

  W_rent = P₀·(1 + d)^T – FV_rent

你也可以将 W_rent 折现回 t = 0 得到 NPV_rent：

  NPV_rent = W_rent / (1 + d)^T = P₀ – [ FV_rent / (1 + d)^T ]

---

### 2.3 决策标准

比较两种方案的净现值（或 T 年后净财富）：

* 如果 NPV_buy > NPV_rent，或同等条件下 W_buy > W_rent，则认为买房（资产增值超过其他费用和机会成本）在经济上更优。
* 否则，租房再加上资金投资收益更具优势。

整理后，可以写出条件（以终值比较为例）：

  买房优势条件：

(1 – c)·P₀·(1 + g)^T – [ P₀ + m·P₀·A ] > P₀·(1 + d)^T – FV_rent

其中 A 为将维护费用转换到 T 年后累计的系数（若按简单复利累积，可近似表示 A = [((1 + d)^T – 1)/d]）。

将 P₀ 消去（前提是 P₀ > 0），决策不依赖于绝对房价，仅依赖于各项比率和利率。

同样，也可以写成以现值为基础的比较条件，具体计算时根据你的资金流整理即可。

---

## 3. 模型二：考虑贷款的购房

若考虑贷款购房，现金流需要细分为：

* t = 0：

  支付首付金额： k·P₀
* t = 1 ～ T（期间）：

  每月需支付按揭月供 M，总年支付为 12·M。其中，按揭还款中，利息部分可以视为成本，而本金部分则转化为最终的股权（相当于隐含储蓄），但同时也存在机会成本，即你用来支付首付和利息的资金本可以获得收益。

  同时，每年仍有维护费用（m·P₀）。
* t = T（出售时）：

  可获得房屋售价为 (1 – c)·P₀·(1 + g)^T，但需要先还清剩余贷款本金 B_T，因而实际到账为：

  Saleₜ = (1 – c)·P₀·(1 + g)^T – B_T

因此，贷款购房的净现值（或 T 年后的净收益）大致为：

  NPV_buy_mortgage = – k·P₀ – Σₜ₌₁^T { [ 每年其他“净成本” ] / (1 + d)ᵗ } + [ Saleₜ / (1 + d)ᵀ ]

其中“每年其他净成本”包括：

• 按揭还款中扣除本金部分后的利息成本（也可视为实际支付的利息）

• 维护费用 m·P₀

由于贷款还款的本金部分最终转化为房屋净资产，因此只将利息和其他费用作为成本处理。

计算时需要构造详细的贷款摊销表，精确得出 T 年内已支付的利息总额以及 T 年末剩余本金 B_T。

最终，同样的，将 NPV_buy_mortgage 与租房方案的 NPV_rent 做比较，以判断哪种方式经济上更优。

---

## 4. 使用模型做决策

你可以按照下面的步骤来应用这一模型：

1. 根据你所在市场和个人情况确定各项参数（房价、租金、维护费率、利率、预期增值率、贴现率等）。
2. 对于全款购房：

   * 计算买房方案的净现值：

     NPV_buy = – P₀ – Σₜ₌₁^T [ m·P₀ / (1 + d)ᵗ ] + [ (1 – c)·P₀·(1 + g)^T ] / (1 + d)ᵀ

   * 计算租房方案中 T 年后的净财富（或现值）：

     FV_rent = Σₜ₌₁^T [ R₀·(1 + g_R)^(t – 1) × (1 + d)^(T – t) ]
     W_rent = P₀·(1 + d)^T – FV_rent
   或折现为 NPV_rent = P₀ – [ FV_rent / (1 + d)^T ]
3. 如果考虑贷款购房，则建立贷款摊销模型，计算 T 年内已支付的利息、剩余本金 B_T，以及 T 年末可变现金额 Saleₜ，再计算贷款购房的整体净现值 NPV_buy_mortgage。
4. 比较买房与租房两个方案的净现值（或 T 年末的净财富）：

   * 若买房方案（全款或贷款）的 NPV 更高，则从投资角度看买房较优；
   * 若租房方案净财富更高，则租房（并将资金用于其他投资）的经济收益更好。

---

## 5. 小结

* 【全款购房】时，你需要验证下面的不等式（以现值形式）：

  – P₀ – Σₜ₌₁^T [ m·P₀/(1+d)ᵗ ] + [ (1 – c)·P₀·(1+g)^T/(1+d)ᵀ ] > P₀ – [ 1/(1+d)ᵀ × Σₜ₌₁^T { R₀·(1+g_R)^(t–1)·(1+d)^(T–t) } ]

* 【贷款购房】时则需在上述基础上引入首付、贷款月供以及最终还款余额的计算。这部分需要构造一个详细的贷款摊销表，将按揭还款细分为利息成本和本金归还，后者最终体现在 T 年末可变现的房屋净资产中。

通过上述模型，将各项成本与收益全面考虑，并以贴现后的净现值作比较，就可以为租房与买房提供量化的决策依据。

---

这就是一个简化但较为完整的决策模型，供你根据实际数据代入计算并做出判断。希望这对你做出“租房 VS 买房”的经济决策有所帮助！
