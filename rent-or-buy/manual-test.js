// 手动测试脚本 - 验证核心计算功能
const {
    calculateMortgageNPV,
    calculateRentNPV,
    calculateLoanDetails,
    findThreshold
} = require('./calculator.js');

console.log('🧪 开始手动测试核心功能...\n');

// 测试用例1: 基本功能测试
console.log('=== 测试用例1: 基本功能测试 ===');
const testCase1 = {
    P0: 8000000, // 800万房价
    T: 30,       // 30年持有
    g: 0.03,     // 3%年增值率
    R0: 120000,  // 12万年租金
    gR: 0.05,    // 5%租金增长率
    k: 0.25,     // 25%首付
    rm: 0.033,   // 3.3%贷款利率
    n: 30,       // 30年贷款期限
    m: 0.001,    // 0.1%维护费率
    d: 0.02,     // 2%投资收益率
    transactionCost: 0.005, // 0.5%交易成本
    paymentType: 'equal_installment'
};

try {
    // 测试贷款购房NPV计算
    const mortgageResult = calculateMortgageNPV(
        testCase1.P0, testCase1.T, testCase1.g, testCase1.k, 
        testCase1.rm, testCase1.n, testCase1.m, testCase1.d, 
        testCase1.paymentType, testCase1.transactionCost
    );
    console.log(`✅ 贷款购房NPV: ${(mortgageResult.finalNPV / 10000).toFixed(2)}万元`);
    
    // 测试租房NPV计算
    const rentResult = calculateRentNPV(
        testCase1.P0, testCase1.R0, testCase1.T, testCase1.gR, testCase1.d
    );
    console.log(`✅ 租房NPV: ${(rentResult.finalNPV / 10000).toFixed(2)}万元`);
    
    // 比较结果
    const difference = mortgageResult.finalNPV - rentResult.finalNPV;
    const betterOption = difference > 0 ? '贷款购房' : '租房';
    console.log(`💡 推荐方案: ${betterOption} (差额: ${(Math.abs(difference) / 10000).toFixed(2)}万元)`);
    
    // 测试贷款详情计算
    const loanDetails = calculateLoanDetails(
        testCase1.P0, testCase1.k, testCase1.rm, testCase1.n, testCase1.paymentType
    );
    console.log(`📊 贷款金额: ${(loanDetails.amount / 10000).toFixed(2)}万元`);
    console.log(`📊 月供: ${(loanDetails.monthlyPayment / 10000).toFixed(2)}万元`);
    console.log(`📊 总利息: ${(loanDetails.totalInterest / 10000).toFixed(2)}万元`);
    
} catch (error) {
    console.log(`❌ 测试用例1失败: ${error.message}`);
}

console.log('\n=== 测试用例2: 等额本金还款方式 ===');
try {
    const mortgageResult2 = calculateMortgageNPV(
        testCase1.P0, testCase1.T, testCase1.g, testCase1.k, 
        testCase1.rm, testCase1.n, testCase1.m, testCase1.d, 
        'equal_principal', testCase1.transactionCost
    );
    console.log(`✅ 等额本金贷款购房NPV: ${(mortgageResult2.finalNPV / 10000).toFixed(2)}万元`);
    
    const loanDetails2 = calculateLoanDetails(
        testCase1.P0, testCase1.k, testCase1.rm, testCase1.n, 'equal_principal'
    );
    console.log(`📊 首月还款: ${(loanDetails2.firstMonthPayment / 12 / 10000).toFixed(2)}万元`);
    console.log(`📊 末月还款: ${(loanDetails2.lastMonthPayment / 12 / 10000).toFixed(2)}万元`);
    
} catch (error) {
    console.log(`❌ 测试用例2失败: ${error.message}`);
}

console.log('\n=== 测试用例3: 极端情况测试 ===');
// 测试高房价增值率情况
try {
    const highAppreciationResult = calculateMortgageNPV(
        testCase1.P0, 10, 0.1, testCase1.k, // 10%年增值率，10年持有
        testCase1.rm, testCase1.n, testCase1.m, testCase1.d, 
        testCase1.paymentType, testCase1.transactionCost
    );
    console.log(`✅ 高增值率(10%)贷款购房NPV: ${(highAppreciationResult.finalNPV / 10000).toFixed(2)}万元`);
    
    // 测试负增值率情况
    const negativeAppreciationResult = calculateMortgageNPV(
        testCase1.P0, 10, -0.05, testCase1.k, // -5%年增值率
        testCase1.rm, testCase1.n, testCase1.m, testCase1.d, 
        testCase1.paymentType, testCase1.transactionCost
    );
    console.log(`✅ 负增值率(-5%)贷款购房NPV: ${(negativeAppreciationResult.finalNPV / 10000).toFixed(2)}万元`);
    
} catch (error) {
    console.log(`❌ 测试用例3失败: ${error.message}`);
}

console.log('\n=== 测试用例4: 阈值查找功能 ===');
try {
    // 测试简单的阈值查找
    const threshold = findThreshold(-5, 5, 0.0001, 1000, (x) => x * x - 4);
    console.log(`✅ 阈值查找测试 (x²-4=0): ${threshold.toFixed(4)} (期望: 2.0000)`);
    
    // 测试实际的投资收益率阈值
    const investmentThreshold = findThreshold(0.01, 0.1, 0.0001, 1000, (testD) => {
        const mortgageNPV = calculateMortgageNPV(
            testCase1.P0, testCase1.T, testCase1.g, testCase1.k, 
            testCase1.rm, testCase1.n, testCase1.m, testD, 
            testCase1.paymentType, testCase1.transactionCost
        ).finalNPV;
        const rentNPV = calculateRentNPV(testCase1.P0, testCase1.R0, testCase1.T, testCase1.gR, testD).finalNPV;
        return mortgageNPV - rentNPV;
    });
    
    if (investmentThreshold !== null) {
        console.log(`✅ 投资收益率临界值: ${(investmentThreshold * 100).toFixed(2)}%`);
        console.log(`   当投资收益率${investmentThreshold > testCase1.d ? '高于' : '低于'}此值时，${investmentThreshold > testCase1.d ? '租房' : '贷款购房'}更优`);
    } else {
        console.log(`⚠️ 未找到投资收益率临界值`);
    }
    
} catch (error) {
    console.log(`❌ 测试用例4失败: ${error.message}`);
}

console.log('\n=== 测试用例5: 数据一致性验证 ===');
try {
    // 验证NPV数组长度
    const result = calculateMortgageNPV(
        testCase1.P0, testCase1.T, testCase1.g, testCase1.k, 
        testCase1.rm, testCase1.n, testCase1.m, testCase1.d, 
        testCase1.paymentType, testCase1.transactionCost
    );
    
    console.log(`✅ NPV数组长度验证: ${result.yearlyNPV.length} (期望: ${testCase1.T + 1})`);
    console.log(`✅ 初始NPV验证: ${(result.yearlyNPV[0] / 10000).toFixed(2)}万元 (期望: ${-(testCase1.k * testCase1.P0 / 10000).toFixed(2)}万元)`);
    
    // 验证租房NPV初始值
    const rentResult = calculateRentNPV(testCase1.P0, testCase1.R0, testCase1.T, testCase1.gR, testCase1.d);
    console.log(`✅ 租房初始NPV验证: ${(rentResult.yearlyNPV[0] / 10000).toFixed(2)}万元 (期望: ${(testCase1.P0 / 10000).toFixed(2)}万元)`);
    
} catch (error) {
    console.log(`❌ 测试用例5失败: ${error.message}`);
}

console.log('\n=== 测试用例6: 边界条件测试 ===');
try {
    // 测试最小持有期
    const shortTermResult = calculateMortgageNPV(
        testCase1.P0, 1, testCase1.g, testCase1.k, 
        testCase1.rm, testCase1.n, testCase1.m, testCase1.d, 
        testCase1.paymentType, testCase1.transactionCost
    );
    console.log(`✅ 1年持有期NPV: ${(shortTermResult.finalNPV / 10000).toFixed(2)}万元`);
    
    // 测试持有期等于贷款期限
    const fullTermResult = calculateMortgageNPV(
        testCase1.P0, testCase1.n, testCase1.g, testCase1.k, 
        testCase1.rm, testCase1.n, testCase1.m, testCase1.d, 
        testCase1.paymentType, testCase1.transactionCost
    );
    console.log(`✅ 30年持有期(等于贷款期限)NPV: ${(fullTermResult.finalNPV / 10000).toFixed(2)}万元`);
    
    // 测试零维护费用
    const noMaintenanceResult = calculateMortgageNPV(
        testCase1.P0, testCase1.T, testCase1.g, testCase1.k, 
        testCase1.rm, testCase1.n, 0, testCase1.d, 
        testCase1.paymentType, testCase1.transactionCost
    );
    console.log(`✅ 零维护费用NPV: ${(noMaintenanceResult.finalNPV / 10000).toFixed(2)}万元`);
    
} catch (error) {
    console.log(`❌ 测试用例6失败: ${error.message}`);
}

console.log('\n🎯 手动测试完成！');
console.log('如果所有测试都显示✅，说明核心功能正常工作。');
console.log('如果有❌，请检查相应的计算逻辑。');
