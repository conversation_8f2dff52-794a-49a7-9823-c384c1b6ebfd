const {
    calculateMortgageNPV,
    calculateRentNPV,
    calculateLoanDetails,
    findThreshold
} = require('./calculator.js');

describe('Calculator Core Functions', () => {
    describe('calculateRentNPV', () => {
        test('basic rent NPV calculation', () => {
            const P0 = 1000000; // 房价100万
            const R0 = 36000; // 年租金3.6万
            const T = 5; // 5年期限
            const gR = 0.05; // 租金年增长率5%
            const d = 0.06; // 投资收益率6%

            const result = calculateRentNPV(P0, R0, T, gR, d);

            expect(result.yearlyNPV).toHaveLength(T + 1);
            expect(result.yearlyNPV[0]).toBe(P0);
            expect(result.finalNPV).toBeLessThan(P0); // NPV应该小于初始投资
        });

        test('zero rent growth rate', () => {
            const result = calculateRentNPV(1000000, 36000, 5, 0, 0.06);
            expect(result.finalNPV).toBeLessThan(1000000);
        });
    });

    describe('calculateMortgageNPV', () => {
        test('basic mortgage NPV calculation with equal installment', () => {
            const P0 = 1000000; // 房价100万
            const T = 5; // 5年期限
            const g = 0.03; // 房价年增值率3%
            const k = 0.3; // 30%首付
            const rm = 0.045; // 贷款利率4.5%
            const n = 30; // 30年期限
            const m = 0.01; // 维护成本1%
            const d = 0.06; // 投资收益率6%
            const paymentType = 'equal_installment';
            const transactionCost = 0.05; // 交易成本5%

            const result = calculateMortgageNPV(P0, T, g, k, rm, n, m, d, paymentType, transactionCost);

            expect(result.yearlyNPV).toHaveLength(T + 1);
            expect(result.yearlyNPV[0]).toBe(-P0 * k); // 初始NPV应该是首付的负值
        });

        test('basic mortgage NPV calculation with equal principal', () => {
            const result = calculateMortgageNPV(
                1000000, 5, 0.03, 0.3, 0.045, 30, 0.01, 0.06,
                'equal_principal', 0.05
            );

            expect(result.yearlyNPV).toHaveLength(6);
            expect(result.yearlyNPV[0]).toBe(-300000); // 首付30万
        });
    });

    describe('findThreshold', () => {
        test('basic threshold finding', () => {
            const evaluator = (x) => x * x - 4; // 找到2或-2
            const result = findThreshold(-3, 3, 0.0001, 1000, evaluator);

            expect(result).toBeCloseTo(2, 2);
        });

        test('no solution found within range', () => {
            const evaluator = (x) => x + 10; // 永远为正
            const result = findThreshold(-5, 5, 0.0001, 1000, evaluator);

            expect(result).not.toBeNull();
        });

        test('immediate solution found', () => {
            const evaluator = (x) => x; // 在x=0处为0
            const result = findThreshold(-1, 1, 0.0001, 1000, evaluator);

            expect(result).toBeCloseTo(0, 5);
        });
    });

    describe('calculateLoanDetails', () => {
        test('equal installment loan details', () => {
            const P0 = 1000000; // 100万房价
            const k = 0.3; // 30%首付
            const rm = 0.045; // 4.5%年利率
            const n = 30; // 30年期限
            const paymentType = 'equal_installment';

            const result = calculateLoanDetails(P0, k, rm, n, paymentType);

            expect(result.amount).toBe(700000); // 贷款金额
            expect(result.monthlyRate).toBeCloseTo(0.045 / 12, 6);
            expect(result.totalMonths).toBe(360);
            expect(result.paymentType).toBe('equal_installment');
            expect(result.monthlyPayment).toBeGreaterThan(0);
            expect(result.totalPayment).toBeGreaterThan(700000);
            expect(result.totalInterest).toBeGreaterThan(0);
        });

        test('equal principal loan details', () => {
            const P0 = 1000000;
            const k = 0.3;
            const rm = 0.045;
            const n = 30;
            const paymentType = 'equal_principal';

            const result = calculateLoanDetails(P0, k, rm, n, paymentType);

            expect(result.amount).toBe(700000);
            expect(result.paymentType).toBe('equal_principal');
            expect(result.monthlyPrincipal).toBeCloseTo(700000 / 30, 2);
            expect(result.firstMonthPayment).toBeGreaterThan(result.lastMonthPayment);
            expect(result.totalPayment).toBeGreaterThan(700000);
        });
    });

    describe('Edge cases and error handling', () => {
        test('zero house price', () => {
            expect(() => {
                calculateMortgageNPV(0, 5, 0.03, 0.3, 0.045, 30, 0.01, 0.06, 'equal_installment', 0.05);
            }).not.toThrow();
        });

        test('very high appreciation rate', () => {
            const result = calculateMortgageNPV(1000000, 5, 0.5, 0.3, 0.045, 30, 0.01, 0.06, 'equal_installment', 0.05);
            expect(result.finalNPV).toBeGreaterThan(0);
        });

        test('negative appreciation rate', () => {
            const result = calculateMortgageNPV(1000000, 5, -0.1, 0.3, 0.045, 30, 0.01, 0.06, 'equal_installment', 0.05);
            expect(result.finalNPV).toBeLessThan(0);
        });

        test('very short holding period', () => {
            const result = calculateMortgageNPV(1000000, 1, 0.03, 0.3, 0.045, 30, 0.01, 0.06, 'equal_installment', 0.05);
            expect(result.yearlyNPV).toHaveLength(2);
        });

        test('holding period equals loan period', () => {
            const result = calculateMortgageNPV(1000000, 30, 0.03, 0.3, 0.045, 30, 0.01, 0.06, 'equal_installment', 0.05);
            expect(result.yearlyNPV).toHaveLength(31);
        });
    });
});