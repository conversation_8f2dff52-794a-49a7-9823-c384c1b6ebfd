// UI测试脚本 - 在浏览器控制台中运行
// 这个脚本测试计算器的各种功能

console.log('🧪 开始UI功能测试...');

// 测试用例数据
const testCases = [
    {
        name: '标准测试用例',
        data: {
            housePrice: 800,
            years: 30,
            appreciation: 3,
            rentPrice: 12,
            rentGrowth: 5,
            downPayment: 25,
            loanRate: 3.3,
            loanYears: 30,
            maintenance: 0.1,
            investmentReturn: 2,
            transactionCost: 0.5,
            paymentType: 'equal_installment'
        }
    },
    {
        name: '高房价增值测试',
        data: {
            housePrice: 1000,
            years: 20,
            appreciation: 8,
            rentPrice: 15,
            rentGrowth: 3,
            downPayment: 30,
            loanRate: 4.5,
            loanYears: 25,
            maintenance: 0.2,
            investmentReturn: 6,
            transactionCost: 1,
            paymentType: 'equal_principal'
        }
    },
    {
        name: '低增值率测试',
        data: {
            housePrice: 600,
            years: 15,
            appreciation: 1,
            rentPrice: 8,
            rentGrowth: 2,
            downPayment: 20,
            loanRate: 2.8,
            loanYears: 20,
            maintenance: 0.05,
            investmentReturn: 4,
            transactionCost: 0.3,
            paymentType: 'equal_installment'
        }
    }
];

// 辅助函数：设置输入值
function setInputValue(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.value = value;
        // 触发change事件
        element.dispatchEvent(new Event('change', { bubbles: true }));
        return true;
    }
    return false;
}

// 辅助函数：获取输入值
function getInputValue(id) {
    const element = document.getElementById(id);
    return element ? element.value : null;
}

// 辅助函数：检查错误消息
function checkErrorMessages() {
    const errors = [];
    const errorElements = document.querySelectorAll('.error-message[style*="block"]');
    errorElements.forEach(el => {
        if (el.style.display === 'block') {
            errors.push({
                id: el.id,
                message: el.textContent
            });
        }
    });
    return errors;
}

// 测试函数：输入验证
function testInputValidation() {
    console.log('📝 测试输入验证...');
    
    // 测试无效输入
    const invalidTests = [
        { id: 'housePrice', value: -100, expected: '请输入有效的房屋总价' },
        { id: 'years', value: 0, expected: '请输入有效的持有年数' },
        { id: 'appreciation', value: 25, expected: '请输入有效的年增值率' },
        { id: 'rentPrice', value: 0, expected: '请输入有效的年租金' },
        { id: 'downPayment', value: 150, expected: '请输入有效的首付比例' }
    ];
    
    let validationPassed = 0;
    
    invalidTests.forEach(test => {
        setInputValue(test.id, test.value);
        
        // 触发验证
        if (typeof validateInputs === 'function') {
            const isValid = validateInputs();
            if (!isValid) {
                validationPassed++;
                console.log(`✅ ${test.id} 验证正确拒绝了无效值: ${test.value}`);
            } else {
                console.log(`❌ ${test.id} 验证未能拒绝无效值: ${test.value}`);
            }
        }
    });
    
    console.log(`📊 输入验证测试: ${validationPassed}/${invalidTests.length} 通过`);
    return validationPassed === invalidTests.length;
}

// 测试函数：计算功能
function testCalculation(testCase) {
    console.log(`🧮 测试计算功能: ${testCase.name}`);
    
    // 设置所有输入值
    let inputsSet = 0;
    for (const [key, value] of Object.entries(testCase.data)) {
        if (setInputValue(key, value)) {
            inputsSet++;
        }
    }
    
    console.log(`📥 设置了 ${inputsSet} 个输入值`);
    
    // 执行计算
    try {
        if (typeof calculate === 'function') {
            calculate();
            
            // 检查结果是否显示
            const resultsDiv = document.getElementById('results');
            if (resultsDiv && resultsDiv.style.display !== 'none') {
                console.log('✅ 计算成功，结果已显示');
                
                // 检查NPV结果
                const mortgageResult = document.getElementById('mortgageResult');
                const rentResult = document.getElementById('rentResult');
                
                if (mortgageResult && rentResult) {
                    const mortgageContent = mortgageResult.querySelector('.result-content');
                    const rentContent = rentResult.querySelector('.result-content');
                    
                    if (mortgageContent && rentContent) {
                        console.log('✅ 贷款购房和租房结果都已生成');
                        
                        // 检查哪个方案更优
                        const betterOption = document.querySelector('.result-card.better');
                        if (betterOption) {
                            const optionType = betterOption.id === 'mortgageResult' ? '贷款购房' : '租房';
                            console.log(`💡 推荐方案: ${optionType}`);
                        }
                        
                        return true;
                    }
                }
            } else {
                console.log('❌ 计算后结果未显示');
            }
        } else {
            console.log('❌ calculate 函数不存在');
        }
    } catch (error) {
        console.log(`❌ 计算过程中出错: ${error.message}`);
    }
    
    return false;
}

// 测试函数：敏感性分析
function testSensitivityAnalysis() {
    console.log('📈 测试敏感性分析...');
    
    const sensitivityResults = document.getElementById('sensitivityResults');
    if (sensitivityResults && sensitivityResults.innerHTML.trim() !== '') {
        console.log('✅ 敏感性分析结果已生成');
        
        // 检查是否有敏感性分析表格
        const table = sensitivityResults.querySelector('table');
        if (table) {
            const rows = table.querySelectorAll('tbody tr');
            console.log(`📊 敏感性分析包含 ${rows.length} 个参数`);
            
            rows.forEach((row, index) => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 4) {
                    const parameter = cells[0].textContent;
                    const currentValue = cells[1].textContent;
                    const threshold = cells[2].textContent;
                    const sensitivity = cells[3].textContent;
                    console.log(`  ${index + 1}. ${parameter}: 当前${currentValue}, 临界值${threshold}, ${sensitivity}`);
                }
            });
            
            return true;
        }
    }
    
    console.log('❌ 敏感性分析结果未找到或为空');
    return false;
}

// 测试函数：图表显示
function testCharts() {
    console.log('📊 测试图表显示...');
    
    const resultChart = document.getElementById('resultChart');
    const npvTrendChart = document.getElementById('npvTrendChart');
    
    let chartsFound = 0;
    
    if (resultChart) {
        console.log('✅ NPV对比图表元素存在');
        chartsFound++;
    }
    
    if (npvTrendChart) {
        console.log('✅ NPV趋势图表元素存在');
        chartsFound++;
    }
    
    // 检查Chart.js是否加载
    if (typeof Chart !== 'undefined') {
        console.log('✅ Chart.js 库已加载');
        chartsFound++;
    } else {
        console.log('❌ Chart.js 库未加载');
    }
    
    return chartsFound >= 2;
}

// 主测试函数
async function runAllTests() {
    console.log('🚀 开始完整功能测试...');
    console.log('='.repeat(50));
    
    const results = {
        validation: false,
        calculations: [],
        sensitivity: false,
        charts: false
    };
    
    // 1. 测试输入验证
    results.validation = testInputValidation();
    
    // 2. 测试计算功能
    for (const testCase of testCases) {
        const success = testCalculation(testCase);
        results.calculations.push({ name: testCase.name, success });
        
        if (success) {
            // 3. 测试敏感性分析（在成功计算后）
            if (!results.sensitivity) {
                results.sensitivity = testSensitivityAnalysis();
            }
            
            // 4. 测试图表显示
            if (!results.charts) {
                results.charts = testCharts();
            }
        }
        
        // 等待一下再进行下一个测试
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 输出测试总结
    console.log('='.repeat(50));
    console.log('📋 测试总结:');
    console.log(`✅ 输入验证: ${results.validation ? '通过' : '失败'}`);
    console.log(`✅ 计算功能: ${results.calculations.filter(c => c.success).length}/${results.calculations.length} 通过`);
    console.log(`✅ 敏感性分析: ${results.sensitivity ? '通过' : '失败'}`);
    console.log(`✅ 图表显示: ${results.charts ? '通过' : '失败'}`);
    
    const totalTests = 1 + results.calculations.length + 1 + 1;
    const passedTests = (results.validation ? 1 : 0) + 
                       results.calculations.filter(c => c.success).length + 
                       (results.sensitivity ? 1 : 0) + 
                       (results.charts ? 1 : 0);
    
    console.log(`🎯 总体通过率: ${passedTests}/${totalTests} (${Math.round(passedTests/totalTests*100)}%)`);
    
    return results;
}

// 导出测试函数供手动调用
window.testRentOrBuy = {
    runAllTests,
    testInputValidation,
    testCalculation,
    testSensitivityAnalysis,
    testCharts
};

console.log('🔧 测试脚本已加载！');
console.log('💡 使用方法:');
console.log('  - runAllTests() - 运行所有测试');
console.log('  - testInputValidation() - 仅测试输入验证');
console.log('  - testCalculation(testCase) - 测试特定计算用例');
console.log('  - testSensitivityAnalysis() - 测试敏感性分析');
console.log('  - testCharts() - 测试图表显示');
console.log('');
console.log('🚀 要开始完整测试，请运行: testRentOrBuy.runAllTests()');
